/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.constant;

import java.math.BigDecimal;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {

	/**
	 * sword 系统名
	 */
	String SWORD_NAME = "sword";

	/**
	 * saber 系统名
	 */
	String SABER_NAME = "saber";

	/**
	 * 顶级父节点id
	 */
	Long TOP_PARENT_ID = 0L;

	/**
	 * 顶级父节点名称
	 */
	String TOP_PARENT_NAME = "顶级";

	/**
	 * 未封存状态值
	 */
	Integer NOT_SEALED_ID = 0;

	/**
	 * 默认密码
	 */
	String DEFAULT_PASSWORD = "Aa123456";

	/**
	 * 默认密码参数值
	 */
	String DEFAULT_PARAM_PASSWORD = "account.initPassword";

	/**
	 * 默认排序字段
	 */
	String SORT_FIELD = "sort";

	/**
	 * 数据权限类型
	 */
	Integer DATA_SCOPE_CATEGORY = 1;

	/**
	 * 接口权限类型
	 */
	Integer API_SCOPE_CATEGORY = 2;

	String APPLICATION_CLIENT_NAME = "skyworth-client";

	String APPLICATION_TOOLKIT_NAME = "skyworth-toolkit";

	String APPLICATION_AGENT_NAME = "skyworth-agent";

	String APPLICATION_PORTABLE_NAME = "skyworth-portable";
	/**
	 * 系统当前语言
	 */
	String CURRENT_LANGUAGE_ZH = "zh";
	/**
	 * 创维公司
	 */
	String SKY_WORTH = "skyworth";

	String SYMBOL_QUESTION_MARK = "?";
	/**
	 * 语言：英文
	 */
	String CURRENT_LANGUAGE_EN = "en";

	String CURRENT_LANGUAGE_DE = "de";

	String SKYWORTH_SEQUENCE_PREFIX = "redis_seq_";

	String SKYWORTH_SEQUENCE_DATE_FORMAT = "yyyyMMdd";

	String LOGIN_TYPE = "login_type";
	String LOGIN_TYPE_TELEPHONE = "telephone";
	String LOGIN_TYPE_EMAIL = "email";
	//手机注册
	String REGISTER_PHONE = "1";
	String REGISTER_EMAIL = "2";
	// 手机号码格式
	final String PHONE_FORMAT = "\\d+";
	String PRE_FIX_FIRST_PLANT_DEVICE_RELATION = "first:plant:device:";

	String PRE_FIX_FIRST_PLANT_DEVICE_RELATION_NET = "first:net:plant:device:";

	String CLIENT_TENANT_ID = "111111";
	String PORTABLE_TENANT_ID = "222222";
	String SOLPWR_TENANT_ID = "333333";
	// 单个电池包额定容量
	BigDecimal SINGLE_BATTERY_RATED_CAPACITY = new BigDecimal("5.12");


	/**
	 * 邮箱审批通知主题
	 */
	String SUBJECT = "SKYWORTH Financial Contribution List";

	/**
	 * 邮箱审批通知模板
	 */
	String CONTENT = "Dear  %s <br>" +
		" Thank you for placing an order in our company.<br>" +
		" The main purpose of this email is to inform you of the <br>" +
		" payment,and the accompanying documents are the details <br>" +
		" of the fee list.<br>" +
		" Please arrange for payment to be made within 15 days.<br>" +
		" If you still have any questions, please feel free to contact<br>" +
		" our staff .<br><br>" +
		" Thanks again,<br>" +
		" Skyworth ";


	//app类别
	String APP_EPC_TYPE = "1";

	//代理商类别
	String AGENCY_TYPE = "3";

	//安装商(施工人员)类别
	String INSTALL_TYPE = "1";

	//电气工程师类别
	String ELECTRIC_TYPE = "2";


	//安装商代理商电气工程师租户ID
	String AGENT_TENANT_ID = "888888";
	// 内部角色
	String ROLE_TYPE_INNER = "inner";
	String ROLE_TYPE_OUT = "out";
	// app注册类型，agent 为代理商app
	String APP_TYPE_AGENT = "agent";
	String APP_TYPE_PORTABLE = "portable";
	String APP_TYPE_CLIENT = "client";
	String APP_TYPE_SOLPWR = "solpwr";
	String DEFAULT_VALUE_MINUS_ONE = "-1";
	// 是否包含内部角色表示
	String USER_ROLE_INNER_FLAG = "innerRoleFlag";

	int REST_FUL_RESULT_SUCCESS = 200;
	// 分配账号
	String USER_FROM_ALLOCATION = "allocation";
	String USER_FROM_REGISTER_WEB = "registerWeb";
	String SIGN_ADD = "+";

	String PHONE_DELETE_USER_COMMON_KEY = "phone:delete:user:";


	//指派施工任务按钮状态
	String MODIFY_STATUS = "modify";

	String DISABLE_STATUS = "disable";

	String PASS_STATUS = "pass";


	//代理商内部标识
	String INTERIOR_TYPE = "skyworth";

	String BLANK = "";
	// 用户信息发生改变时记录
	String BLADE_USER_CHANGE_PRE = "blade:user:change:pre:";

	String DEFAULT_TIME_ZONE = "time_zone";

	String RESOURCE_ENCRYPTED_KEY = "sms:encrypted:key:";
	String RESOURCE_BLACKLIST_PREFIX = "sms:blackIp:";
	String RESOURCE_REQUEST_COUNT_PREFIX = "sms:rate:limit:request:";
	String FLAG_Y = "Y";
	String FLAG_N = "N";
	// 系统用户账号
	String COMMON_SYSTEM_USER_ACCOUNT = "system";
	// 默认时区
	String COMMON_DEFAULT_TIME_ZONE = "UTC+00:00";
	// 日起始时间
	String COMMON_DAY_START_TIME = " 00:00:00";
	// 日结束时间
	String COMMON_DAY_END_TIME = " 23:59:59";
	// 系统自动恢复(德语)
	String COMMON_SYSTEM_AUTO_RECOVERY_DE = "Automatische Wiederherstellung nach Systemüberprüfung";
	// 系统自动恢复(英语)
	String COMMON_SYSTEM_AUTO_RECOVERY_EN = "Automatic recovery after system check";

	String AUTO_ADJUST = "autoAdjust";

	String WEB_TIME_ZONE_FOR_USER_PRE = "web:timeZone:forUser:pre:";
}
