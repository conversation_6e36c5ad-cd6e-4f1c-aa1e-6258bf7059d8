package org.skyworth.ess.device.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/3/11 14:11:18
 */
@Data
@ApiModel(value = "查询条件", description = "查询条件")
public class InverterReportQueryVO implements Serializable {
	private static final long serialVersionUID = -1;

	@ApiModelProperty(value = "站点id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;

	@ApiModelProperty(value = "电池SN")
	private String batterySerialNumber;

	@ApiModelProperty(value = "开始时间")
	private Date startDateTime;

	@ApiModelProperty(value = "结束时间")
	private Date endDateTime;

	@ApiModelProperty(value = "逆变器SN")
	private String deviceSerialNumber;

	@ApiModelProperty(value = "日期")
	private String date;

	@ApiModelProperty(value = "每日统计查询类型")
	private Integer everyDayStatQueryType;

}

