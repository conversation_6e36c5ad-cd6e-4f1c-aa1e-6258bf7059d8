/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.event.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.event.excel.ImportantEventExcel;
import org.skyworth.ess.event.mapper.ImportantEventMapper;
import org.skyworth.ess.event.service.IImportantEventService;
import org.skyworth.ess.event.vo.ImportantEventVO;
import org.skyworth.ess.timeshift.ITimeShiftService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 重要事件表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Service
public class ImportantEventServiceImpl extends BaseServiceImpl<ImportantEventMapper, ImportantEventEntity> implements IImportantEventService {
	@Autowired
	private ITimeShiftService timeShiftService;

	@Override
	public IPage<ImportantEventVO> selectImportantEventPage(IPage<ImportantEventVO> page, ImportantEventVO ImportantEvent) {
		List<ImportantEventVO> importantEventVOS = baseMapper.selectImportantEventPage(page, ImportantEvent);
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat ("yyyy-MM-dd");
		for(ImportantEventVO vo : importantEventVOS) {
			vo.setImportantEventDate(simpleDateFormat.format(vo.getEventDate()));
		}
		List<Long> plantIdList = importantEventVOS.stream().map(ImportantEventVO::getPlantId).distinct().collect(Collectors.toList());
		timeShiftService.getAndReturnList(importantEventVOS,"eventDate",plantIdList);
		return page.setRecords(importantEventVOS);
	}


	@Override
	public List<ImportantEventExcel> exportImportantEvent(Wrapper<ImportantEventEntity> queryWrapper) {
		List<ImportantEventExcel> ImportantEventList = baseMapper.exportImportantEvent(queryWrapper);
		//ImportantEventList.forEach(ImportantEvent -> {
		//	ImportantEvent.setTypeName(DictCache.getValue(DictEnum.YES_NO, ImportantEvent.getType()));
		//});
		return ImportantEventList;
	}

	@Override
	public void importExcel(List<ImportantEventExcel> data, Boolean isCovered) {
		data.forEach(userExcel -> {
			ImportantEventEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, ImportantEventEntity.class));
			this.save(entity);
		});
	}
}
