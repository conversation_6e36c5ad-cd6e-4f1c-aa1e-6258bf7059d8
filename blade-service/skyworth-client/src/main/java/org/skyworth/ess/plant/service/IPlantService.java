/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.excel.PlantExcel;
import org.skyworth.ess.plant.vo.*;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;

import java.util.List;

/**
 * 站点信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface IPlantService extends BaseService<PlantEntity> {

	PlantVO homePage();

	IPage<PlantVO> page(Query query, PlantVO plantVO);

	List<PlantInstallInfoVO> installedInfo(PlantVO plantVO);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param plant
	 * @return
	 */
	IPage<PlantVO> selectPlantPage(IPage<PlantVO> page, PlantVO plant,  String userType,String listSearchCondition);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<PlantExcel> exportPlant(Wrapper<PlantEntity> queryWrapper);

	/**
	 * 查询详情
	 *
	 * @param plant 入参
	 * @return PlantVO
	 * <AUTHOR>
	 * @since 2023/9/20 13:31
	 **/
	PlantVO view(PlantEntity plant, String language);

	/**
	 * 更新站点信息
	 *
	 * @param plantEntity
	 * @return
	 */
	int updatePlant(PlantEntity plantEntity);

	PlantVO getPlantDetail(PlantEntity plantEntity);

	int updateStatusById(long id, String status, Integer existUserTypeAlarm, Integer existAgentTypeAlarm);

	List<PlantEntity> queryPlant(PlantEntity eq);

	/**
	 * 分组查询站点状态
	 *
	 * @return Map<String, Long>
	 * <AUTHOR>
	 * @since 2023/11/16 18:39
	 **/
	List<JSONObject> queryPlantStatusCount();

	R<List<PlantClientVO>> getPlantByUserInfo(PlantClientVO vo);

	R submitOperationInfo(PlantOperationInfoVO vo);

	/**
	 * 代理商页面查询站点信息
	 *
	 * @param deptId 入参
	 * @param query  分页
	 * @return R<List < PlantAgentViewVO>>
	 * <AUTHOR>
	 * @since 2024/3/7 17:27
	 **/
	IPage<PlantAgentViewVO> getPlantAgentViewInfo(Long deptId, Query query);

	Boolean cleanPlantOperationUserId(List<Long> userIdList);

	Boolean cleanPlantDeptIdOrUsers(JSONObject jsonObject);
}
