/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.plant.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.excel.PlantExcel;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.plant.vo.PlantClientVO;
import org.skyworth.ess.plant.vo.PlantInstallInfoVO;
import org.skyworth.ess.plant.vo.PlantOperationInfoVO;
import org.skyworth.ess.plant.vo.PlantVO;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 站点信息表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("/plant")
@Api(value = "站点信息表", tags = "站点信息表接口")
public class PlantController extends BladeController {

	private final IPlantService plantService;

	private final IAppService appService;

	/**
	 * 站点信息表 自定义分页
	 */
	@GetMapping("/homePage/getData")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "首页", notes = "首页")
	public R<PlantVO> homePage() {
		PlantVO plantVO = plantService.homePage();
		return R.data(plantVO);
	}

	/**
	 * 站点信息表 分页
	 */
	@PostMapping("/list/{pageSize}/{current}")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页查询", notes = "传入plant")
	@PreAuth("hasPermission('client:plant:list')")
	public R<IPage<PlantVO>> list(@RequestBody PlantVO plant,
								  @ApiParam(value = "每页大小", required = true) @PathVariable("pageSize") int pageSize,
								  @ApiParam(value = "当前页", required = true) @PathVariable("current") int current) {
		Query query = new Query();
		query.setCurrent(current);
		query.setSize(pageSize);
		return R.data(plantService.page(query, plant));
	}

	/**
	 * 站点信息表 分页
	 */
	@PostMapping("/installedInfo")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "站点安装信息", notes = "站点安装信息")
	@PreAuth("hasPermission('client:plant:detail')")
	public R<List<PlantInstallInfoVO>> plantInstalledInfo(@RequestBody PlantVO plant) {
		return R.data(plantService.installedInfo(plant));
	}

	/**
	 * 站点信息表 详情+
	 */
	@GetMapping("/view")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查询详情", notes = "传入plant")
	@PreAuth("hasPermission('client:plant:detail')")
	public R<PlantVO> view(PlantEntity plant) {
		PlantVO detail = plantService.view(plant, CommonUtil.getCurrentLanguage());
		return R.data(detail);
	}

	/**
	 * 站点信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入BatteryExitFactoryInfo")
	@PreAuth("hasPermission('client:plant:detail')")
	public R<PlantVO> detail(PlantEntity plant) {
		PlantVO detail = plantService.getPlantDetail(plant);
		return R.data(detail);
	}
	/**
	 * 站点信息表 自定义分页
	 */
//	@GetMapping("/page")
//	@ApiOperationSupport(order = 3)
//	@ApiOperation(value = "分页查询", notes = "传入plant")
//	public R<IPage<PlantVO>> page(PlantVO plant, Query query) {
//		IPage<PlantVO> pages = plantService.selectPlantPage(Condition.getPage(query), plant);
//		return R.data(pages);
//	}

	/**
	 * 站点信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入plant")
	@PreAuth("hasPermission('client:plant:add')")
	public R save(@Valid @RequestBody PlantEntity plant) {
		return R.status(plantService.save(plant));
	}

	/**
	 * 站点信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入plant")
	@PreAuth("hasPermission('client:plant:update')")
	public R update(@Valid @RequestBody PlantEntity plant) {
		plant.setUpdateTime(DateUtil.now());
		return R.status(plantService.updateById(plant));
	}

	/**
	 * 站点信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入plant")
	@PreAuth("hasPermission('client:plant:update')")
	public R submit(@Valid @RequestBody PlantEntity plant) {
		plant.setUpdateTime(DateUtil.now());
		return R.status(plantService.saveOrUpdate(plant));
	}

	/**
	 * 站点信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('client:plant:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return appService.deletePlant(ids);
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-plant")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入plant")
	@PreAuth("hasPermission('client:plant:export')")
	public void exportPlant(@ApiIgnore @RequestParam Map<String, Object> plant, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<PlantEntity> queryWrapper = Condition.getQueryWrapper(plant, PlantEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Plant::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(PlantEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<PlantExcel> list = plantService.exportPlant(queryWrapper);
		ExcelUtil.export(response, "站点信息表数据" + DateUtil.time(), "站点信息表数据表", list, PlantExcel.class);
	}

	@PostMapping("/plantByUserInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "人员站点", notes = "人员站点")
	public R<List<PlantClientVO>>  plantByUserInfo(@RequestBody PlantClientVO vo) {
		return plantService.getPlantByUserInfo(vo);
	}

	@PostMapping("/submitOperationInfo")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "提交站点运维信息", notes = "提交站点运维信息")
	public R submitOperationInfo(@Valid @RequestBody PlantOperationInfoVO vo) {
		return plantService.submitOperationInfo(vo);
	}
}
