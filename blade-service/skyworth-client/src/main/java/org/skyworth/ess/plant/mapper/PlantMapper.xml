<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.plant.mapper.PlantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="plantResultMap" type="org.skyworth.ess.plant.entity.PlantEntity">
        <result column="id" property="id"/>
        <result column="plant_name" property="plantName"/>
        <result column="country_code" property="countryCode"/>
        <result column="province_code" property="provinceCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="county_code" property="countyCode"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="time_zone" property="timeZone"/>
        <result column="photovoltaic_number" property="photovoltaicNumber"/>
        <result column="device_number" property="deviceNumber"/>
        <result column="battery_number" property="batteryNumber"/>
        <result column="charging_station" property="chargingStation"/>
        <result column="install_capacity" property="installCapacity"/>
        <result column="install_date" property="installDate"/>
        <result column="install_team" property="installTeam"/>
        <result column="operation_team" property="operationTeam"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="plant_status" property="plantStatus"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="operation_company_id" property="operationCompanyId"/>
        <result column="operation_user_id" property="operationUserId"/>
        <result column="install_team_id" property="installTeamId"/>
        <result column="exist_user_type_alarm" property="existUserTypeAlarm"/>
        <result column="exist_agent_type_alarm" property="existAgentTypeAlarm"/>
    </resultMap>
    <update id="updateStatusById">
        update plant
        set plant_status=#{status}
        <if test="existUserTypeAlarm != null">
            ,exist_user_type_alarm = #{existUserTypeAlarm}
        </if>
        <if test="existAgentTypeAlarm != null">
            ,exist_agent_type_alarm = #{existAgentTypeAlarm}
        </if>
        where id = #{id}
        <if test="existUserTypeAlarm == null and existAgentTypeAlarm == null">
            and (plant_status!='2' or plant_status is null)
        </if>
    </update>


    <select id="selectPlantPage" resultMap="plantVOMap">
        select id, plant_name, country_code, province_code, city_code, county_code, detail_address, time_zone,
        photovoltaic_number, device_number, battery_number, charging_station, install_capacity, install_date,
        install_team, operation_team, create_user_account, update_user_account, tenant_id,
        create_user, create_dept, create_time, update_user, update_time, status, is_deleted ,is_parallel_mode , operation_company_id,
        operation_user_id, install_team_id, exist_user_type_alarm, exist_agent_type_alarm,
        <choose>
            <when test="userType == 'user'">
                if(plant_status = 2,if(exist_user_type_alarm = 1,2,1),plant_status) as plant_status
            </when>
            <when test="userType == 'agent'">
                if(plant_status = 2,if(exist_agent_type_alarm = 1,2,1),plant_status) as plant_status
            </when>
            <otherwise>
                plant_status
            </otherwise>
        </choose>
        from plant p where
        is_deleted =0
        <if test="listSearchCondition!=null and listSearchCondition!=''">
            and ( p.plant_name like  CONCAT(#{listSearchCondition}, '%')
                or p.id in (
                    select plant_id from wifi_stick_plant w where w.is_deleted =0  and w.device_serial_number like CONCAT(#{listSearchCondition}, '%')
                )
            )
        </if>
        <if test="params.deviceSerialNumber!=null and params.deviceSerialNumber!=''">
            and p.id in (
                    select w.plant_id from wifi_stick_plant w where w.is_deleted =0  and w.device_serial_number like
            CONCAT(#{params.deviceSerialNumber}, '%')
                    union all
                    select c.plant_id  from battery_map_device c where c.is_deleted =0 and c.battery_serial_number like CONCAT(#{params.deviceSerialNumber}, '%')
                )
        </if>
        <if test="params.countryCode!=null and params.countryCode!=''">
            and country_code = #{params.countryCode}
        </if>
        <if test="params.provinceCode!=null and params.provinceCode!=''">
            and province_code = #{params.provinceCode}
        </if>
        <if test="params.cityCode!=null and params.cityCode!=''">
            and city_code = #{params.cityCode}
        </if>
        <if test="params.countyCode!=null and params.countyCode!=''">
            and county_code = #{params.countyCode}
        </if>
        <if test="params.createUserAccount!=null and params.createUserAccount!=''">
            and create_user_account = #{params.createUserAccount}
        </if>
        <if test="params.plantStatus!=null and params.plantStatus!=''">
            <if test="params.plantStatus == 0">
                and plant_status = 0
            </if>
            <if test="params.plantStatus == 1">
                and (plant_status = 1
                <choose>
                    <when test="userType=='user'">
                        or (plant_status = 2 and exist_user_type_alarm = 0)
                    </when>
                    <when test="userType=='agent'">
                        or (plant_status = 2 and exist_agent_type_alarm = 0)
                    </when>
                    <otherwise>
                        and 1=1
                    </otherwise>
                </choose>
                )
            </if>
            <if test="params.plantStatus == 2">
                and plant_status = 2
                <choose>
                    <when test="userType=='user'">
                        and exist_user_type_alarm = 1
                    </when>
                    <when test="userType=='agent'">
                        and exist_agent_type_alarm = 1
                    </when>
                    <otherwise>
                        and 1=1
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="params.plantName!=null and params.plantName!=''">
            and plant_name LIKE CONCAT(#{params.plantName}, '%')
        </if>
        <if test="params.deptId!=null and params.deptId!= ''">
            and (
            find_in_set(p.operation_company_id ,#{params.deptId}) != 0
            or ( create_user =#{params.createUser} and operation_company_id is null )
            or ( create_user =#{params.createUser} and find_in_set(p.operation_company_id ,#{params.deptId}) = 0 )
            )
        </if>
        <if test="params.createUser!=null and params.createUser!=''">
            and id not in (select plant_id from plant_agent_unauthorized_user where
            unauthorized_user_id=#{params.createUser} and is_deleted =0 )
        </if>
        <if test="params.createUserIds!=null and params.createUserIds.size() > 0 ">
            and create_user in
            <foreach item="item" index="index" collection="params.createUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.phoneUserIds!=null and params.phoneUserIds.size() > 0 ">
            and create_user in
            <foreach item="item" index="index" collection="params.phoneUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.operationUserIds!=null and params.operationUserIds.size() > 0 ">
            and operation_user_id in
            <foreach item="item" index="index" collection="params.operationUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.operationCompanyDeptId!=null">
            and operation_company_id = #{params.operationCompanyDeptId}
        </if>
        order by update_time desc, id desc
    </select>


    <select id="exportPlant" resultType="org.skyworth.ess.plant.excel.PlantExcel">
        SELECT *
        FROM plant ${ew.customSqlSegment}
    </select>

    <update id="updatePlant">
        update plant set
        <if test="params.installDate!=null">
            install_date = #{params.installDate},
        </if>
        <if test="params.installTeamId!=null">
            install_team_id = #{params.installTeamId},
        </if>
        <if test="params.photovoltaicNumber!=null">
            photovoltaic_number=IFNULL(photovoltaic_number,0)+#{params.photovoltaicNumber},
        </if>
        <if test="params.deviceNumber!=null">
            device_number=IFNULL(device_number,0)+#{params.deviceNumber},
        </if>
        <if test="params.batteryNumber!=null">
            battery_number=IFNULL(battery_number,0)+#{params.batteryNumber},
        </if>
        <if test="params.chargingStation!=null">
            charging_station=IFNULL(charging_station,0)+#{params.chargingStation},
        </if>
        <if test="params.updateUserAccount!=null">
            update_user_account=#{params.updateUserAccount},
        </if>
        <if test="params.updateUser!=null">
            update_user=#{params.updateUser},
        </if>
        <if test="params.existAgentTypeAlarm!=null">
            exist_agent_type_alarm=#{params.existAgentTypeAlarm},
        </if>
        <if test="params.existUserTypeAlarm!=null">
            exist_user_type_alarm=#{params.existUserTypeAlarm},
        </if>
        <if test="params.plantStatus!=null and params.plantStatus!=''">
            plant_status=#{params.plantStatus},
        </if>
        <if test="params.isDeleted!=null">
            is_deleted=#{params.isDeleted},
        </if>
        <if test="params.isParallelMode!=null">
            is_parallel_mode=#{params.isParallelMode},
        </if>
        <!--        <if test="params.updateTime!=null">-->
        <!--            update_time=#{params.updateTime},-->
        <!--        </if>-->
        update_time=now()
        where
        <if test="params.id!=null">
            id=#{params.id}
        </if>
        <if test="params.createUser!=null">
            create_user=#{params.createUser}
        </if>
    </update>

    <select id="queryPlant" resultMap="plantResultMap">
        select * from plant where is_deleted=0
        <if test="params.plantName !=null and params.plantName!=''">
            and plant_name = #{params.plantName}
        </if>
        <if test="params.createUserAccount!=null and params.createUserAccount!=''">
            and create_user_account = #{params.createUserAccount}
        </if>
        <if test="params.createUser!=null ">
            and create_user = #{params.createUser}
        </if>
        <if test="params.createUserList!=null and params.createUserList.size() > 0 ">
            and create_user in
            <foreach item="item" index="index" collection="params.createUserList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="queryPlantIsDelete" resultMap="plantResultMap">
        select * from plant where is_deleted = 1 and id = #{id}
    </select>

    <resultMap id="plantVOMap" type="org.skyworth.ess.plant.vo.PlantVO">
        <result column="id" property="id"/>
        <result column="plant_name" property="plantName"/>
        <result column="country_code" property="countryCode"/>
        <result column="province_code" property="provinceCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="county_code" property="countyCode"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="time_zone" property="timeZone"/>
        <result column="photovoltaic_number" property="photovoltaicNumber"/>
        <result column="device_number" property="deviceNumber"/>
        <result column="battery_number" property="batteryNumber"/>
        <result column="charging_station" property="chargingStation"/>
        <result column="install_capacity" property="installCapacity"/>
        <result column="install_date" property="installDate"/>
        <result column="install_team" property="installTeam"/>
        <result column="operation_team" property="operationTeam"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="plant_status" property="plantStatus"/>
        <result column="operation_company_id" property="operationCompanyId"/>
        <result column="operation_user_id" property="operationUserId"/>
        <result column="install_team_id" property="installTeamId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="exist_user_type_alarm" property="existUserTypeAlarm"/>
        <result column="exist_agent_type_alarm" property="existAgentTypeAlarm"/>
        <result column="is_parallel_mode" property="isParallelMode"/>
    </resultMap>

    <select id="queryPlantStatusCount" resultType="com.alibaba.fastjson.JSONObject">
        select p.plant_status        as plantStatus,
               count(p.plant_status) as plantStatusCount
        from plant p
        where p.is_deleted = 0
          and p.plant_status is not null
        group by p.plant_status
    </select>

    <select id="getPlantAgentViewInfo" resultType="org.skyworth.ess.plant.vo.PlantAgentViewVO">
        select p.id,
               p.plant_name,
               p.country_code,
               p.province_code,
               p.city_code,
               p.detail_address,
               p.create_user
        from plant p
        where p.operation_company_id = #{deptId}
          and p.is_deleted = 0
        order by p.id desc
    </select>

    <update id="cleanPlantOperationUserId">
        update plant set operation_user_id = null,update_time = now() where
        is_deleted = 0 and operation_user_id in
        <foreach collection="userIdList" index="index" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <update id="cleanPlantOperationUser">
        update
        plant t
        set
        t.operation_user_id = null,
        t.update_time = now()
        where
        t.operation_company_id = #{deptId}
        and t.operation_user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and t.is_deleted = 0;

        update
        plant_agent_unauthorized_user t,
        plant b
        set
        t.is_deleted = 1,
        t.update_time = now()
        where
        t.plant_id = b.id
        and b.operation_company_id = #{deptId}
        and t.unauthorized_user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and b.is_deleted = 0
        and t.is_deleted = 0;
    </update>

    <update id="cleanPlantDeptIdAndOperationUser">
        update
        plant t
        set
        t.operation_user_id = null,
        t.operation_company_id = null,
        t.update_time = now()
        where
        t.is_deleted = 0
        and t.operation_company_id in
        <foreach collection="deptIds.split(',')" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ;

        update
        plant_agent_unauthorized_user t,
        plant b
        set
        t.is_deleted = 1,
        t.update_time = now()
        where
        t.plant_id = b.id
        and b.operation_company_id in
        <foreach collection="deptIds.split(',')" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and b.is_deleted = 0
        and t.is_deleted = 0;
    </update>
</mapper>
