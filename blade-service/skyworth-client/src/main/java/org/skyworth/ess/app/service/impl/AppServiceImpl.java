package org.skyworth.ess.app.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import me.xdrop.fuzzywuzzy.FuzzySearch;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.service.IAlarmLogService;
import org.skyworth.ess.alarmoperationrecord.service.IAlarmLogOperationRecordService;
import org.skyworth.ess.app.service.IAppService;
import org.skyworth.ess.app.vo.*;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.service.IBatteryEverydayTotalService;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.battery.service.impl.BatteryExitFactoryInfoServiceImpl;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.battery.vo.BatteryMapDeviceVO;
import org.skyworth.ess.company.fegin.AgentClientBiz;
import org.skyworth.ess.constant.DeviceEventContentMultiLanguageEnum;
import org.skyworth.ess.constant.DeviceEventRemarkMultiLanguageEnum;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.service.DeviceLog22ByDorisService;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.device.service.*;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.event.service.IImportantEventService;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.ota.service.IOtaUpdatePackService;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.plant.vo.*;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.common.cache.CacheNames;
import org.springblade.common.cache.EmailCacheNamesEnum;
import org.springblade.common.constant.*;
import org.springblade.common.mail.SendMail;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.StatusDisplayUtil;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.redis.lock.LockType;
import org.springblade.core.redis.lock.RedisLockClient;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.*;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service

@Slf4j
public class AppServiceImpl implements IAppService {

	@Resource
	private IPlantService plantService;

	@Resource
	private IImportantEventService importantEventService;

	@Resource
	private BladeRedis bladeRedis;
	@Resource
	private SendMail sendMail;
	@Resource
	private IWifiStickPlantService wifiStickPlantService;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	@Resource
	private IDevice21Service device21Service;
	@Resource
	private IBatteryMapDeviceService batteryMapDeviceService;
	@Resource
	private IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	@Resource
	private IBatteryCurrentStatusService batteryCurrentStatusService;
	@Resource
	private IBatteryEverydayTotalService batteryEverydayTotalService;
	@Resource
	private IDevice23Service device23Service;
	@Resource
	private IOtaUpdatePackService otaUpdatePackService;
	@Resource
	private IAttachmentInfoClient attachmentInfoService;
	@Resource
	private DeviceLog22ByDorisService deviceLog22ByDorisService;
	@Resource
	private IDeviceCurrentStatusService deviceCurrentStatusService;
	@Resource
	private ISysClient sysClient;
	@Resource
	private IDevice24Service device24Service;
	@Resource
	private IUserClient userClient;
	@Resource
	private IDeviceIssueBiz deviceIssueBiz;
	@Resource
	private AppReportServiceImpl appReportServiceImpl;
	@Resource
	private AgentClientBiz agentClient;
	@Resource
	private IUserSearchClient userSearchClient;
	@Resource
	private TimeZoneDeviceService timeZoneDeviceService;
	@Resource
	private RedisLockClient client;

	// 根据实际需求调整线程池参数
	@Autowired
	@Qualifier("commonThreadPool")
	private ThreadPoolExecutor customExecutor;

	@Resource
	private IAlarmLogService alarmLogService;


	@Override
	public String addPlant(PlantEntity plant) {
		BladeUser user = AuthUtil.getUser();
		plant.setCreateUserAccount(user.getAccount());
		plant.setCreateUser(user.getUserId());
//		plant.setInstallDate(new Date());
		plant.setPlantStatus(BizConstant.CHAR_ZERO);
		plantService.save(plant);
		ImportantEventEntity importantEventEntity = new ImportantEventEntity();
		importantEventEntity.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_PLANT);
		importantEventEntity.setPlantId(plant.getId());
		importantEventEntity.setEventContent(DeviceEventContentMultiLanguageEnum.PLANT_EVENT_CREATE.getDeviceEventByLang(CommonUtil.getCurrentLanguage()));
		importantEventEntity.setEventDate(new Date());
		importantEventEntity.setCreateUserAccount(user.getAccount());
		importantEventEntity.setCreateUser(user.getUserId());
		importantEventService.save(importantEventEntity);
		return StrUtil.toString(plant.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Long addWifiDongle(WifiStickPlantEntity wifiStickPlantEntity) {
		boolean redisLock = false;
		String deviceSerialNumber = wifiStickPlantEntity.getDeviceSerialNumber();
		String redisKey = "wifi_dongle_lock_processed:" + deviceSerialNumber;
		try {
			// 给SN加锁，如果加锁成功，则SN未被扫码添加；失败该SN已经调用接口添加，直接返回-500L;
			// 不影响其他SN继续添加
			redisLock = client.tryLock(redisKey, LockType.FAIR, 0, 10, TimeUnit.SECONDS);
			if (redisLock) {
				PlantEntity byId = plantService.getById(wifiStickPlantEntity.getPlantId());
				if (byId == null) {
					return -200L;
				}
				QueryWrapper<DeviceExitFactoryInfoEntity> queryWrapper = new QueryWrapper<>();
				queryWrapper.eq(DatabaseFieldConstant.DEVICE_SERIAL_NUMBER, wifiStickPlantEntity.getDeviceSerialNumber());
				List<DeviceExitFactoryInfoEntity> list = deviceExitFactoryInfoService.list(queryWrapper);
				if (CollectionUtils.isEmpty(list)) {
					return -100L;
				}
				DeviceExitFactoryInfoEntity newInverterInfo = list.get(0);
				// 根据站点id，查出站点下的所有逆变器以及它的型号，如果新添加的逆变器型号与旧的逆变器不同，则不能作为并机进行添加
				WifiStickPlantEntity queryWifi = new WifiStickPlantEntity();
				queryWifi.setPlantId(wifiStickPlantEntity.getPlantId());
				List<WifiStickPlantEntity> queryWifiList = wifiStickPlantService.list(Condition.getQueryWrapper(queryWifi));
				if (queryWifiList.size() == BizConstant.CLIENT_INVERTER_MAX_PARALLEL) {
					return -300L;
				}
				// 如果站点下已有逆变器，则判断该逆变器的型号是否一致，不一致则报错
				if (!queryWifiList.isEmpty()) {
					WifiStickPlantEntity wifiStickPlant = queryWifiList.get(0);
					DeviceExitFactoryInfoEntity queryDeviceExit = new DeviceExitFactoryInfoEntity();
					queryDeviceExit.setDeviceSerialNumber(wifiStickPlant.getDeviceSerialNumber());
					DeviceExitFactoryInfoEntity oldInverterInfo = deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(queryDeviceExit));
					if (!oldInverterInfo.getDeviceType().equals(newInverterInfo.getDeviceType())) {
						return -400L;
					}
				}


				BladeUser user = AuthUtil.getUser();
				// 如果未扫描过逆变器，则保存
				wifiStickPlantEntity.setCreateUserAccount(user.getAccount());
				wifiStickPlantEntity.setCreateUser(user.getUserId());
				wifiStickPlantEntity.setWifiStickStatus(BizConstant.CLIENT_WIFI_STICK_STATUS_OFFLINE);
				wifiStickPlantService.save(wifiStickPlantEntity);
				PlantEntity updatePlantEntity = new PlantEntity();
				updatePlantEntity.setId(wifiStickPlantEntity.getPlantId());
				updatePlantEntity.setDeviceNumber(1);
				updatePlantEntity.setUpdateTime(new Date());
				updatePlantEntity.setUpdateUserAccount(user.getAccount());
				updatePlantEntity.setUpdateUser(user.getUserId());
				plantService.updatePlant(updatePlantEntity);
				PlantEntity plantById = plantService.getById(wifiStickPlantEntity.getPlantId());
				this.saveImportant(wifiStickPlantEntity, user, plantById);

				//绑定逆变器时同步修改质保日期
				DeviceExitFactoryInfoEntity deviceExitFactoryInfo = list.get(0);
				LocalDate currentDate = LocalDate.now();
				//激活日期
				String activationDate = currentDate.format(BatteryExitFactoryInfoServiceImpl.FORMATTER);
				//质保开始日期
				String warrantyStartDate;
				warrantyStartDate = activationDate;
				// 如果未设置质保截止日期，则代表没有激活过，此时用质保开始时间+质保年限作为质保截止日期。
				if (StringUtils.isEmpty(deviceExitFactoryInfo.getWarrantyDeadline())) {
					LocalDate date = LocalDate.parse(warrantyStartDate);
					LocalDate warrantyEndDate = date.plusYears(Long.parseLong(deviceExitFactoryInfo.getQualityGuaranteeYear()));
					deviceExitFactoryInfo.setWarrantyDeadline(warrantyEndDate.toString());
				}
				LambdaUpdateWrapper<DeviceExitFactoryInfoEntity> update = Wrappers.<DeviceExitFactoryInfoEntity>update().lambda()
					.set(DeviceExitFactoryInfoEntity::getStatus, 1)
					.set(DeviceExitFactoryInfoEntity::getUpdateUserAccount, user.getAccount())
					.set(DeviceExitFactoryInfoEntity::getUpdateUser, user.getUserId())
					.set(DeviceExitFactoryInfoEntity::getWarrantyDeadline, deviceExitFactoryInfo.getWarrantyDeadline())
					.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, deviceSerialNumber);
				if (StringUtils.isEmpty(deviceExitFactoryInfo.getActivationDate())) {
					update.set(DeviceExitFactoryInfoEntity::getActivationDate, activationDate);
				}
				if (StringUtils.isEmpty(deviceExitFactoryInfo.getWarrantyStartDate())) {
					update.set(DeviceExitFactoryInfoEntity::getWarrantyStartDate, warrantyStartDate);
				}
				deviceExitFactoryInfoService.update(update);
				return wifiStickPlantEntity.getId();
			}else{
				log.error("重复的接口请求");
				return -500L;
			}
		} catch (InterruptedException e) {
			log.error("redis分布式锁加锁失败", e);
		}finally {
			if (redisLock){
				client.unLock(redisKey, LockType.FAIR);
			}
		}
		return 0L;
	}

	private String getAllAddressByPlant(PlantEntity plantById) {
		// 增加详细地址
		List<String> regionCodeList = new ArrayList<>();
		regionCodeList.add(plantById.getCountryCode());
		regionCodeList.add(plantById.getProvinceCode());
		regionCodeList.add(plantById.getCityCode());
		regionCodeList.add(plantById.getCountyCode());
		return buildDeviceAllAddress(regionCodeList, plantById);
	}

	private String buildDeviceAllAddress(List<String> regionCodeList, PlantEntity plantEntity) {
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			StringBuilder address = new StringBuilder(plantEntity.getDetailAddress() == null ? "" : plantEntity.getDetailAddress() + " ");
			List<Region> regionList = sysClient.getRegionList(regionCodeNotNullList).getData();
			Collections.reverse(regionList);
			if (CollectionUtils.isEmpty(regionList)) {
				return "";
			}
			for (Region region : regionList) {
				if (region.getCode().equalsIgnoreCase(plantEntity.getCountryCode())) {
					address.append(region.getName()).append(" ");
				}
				if (region.getCode().equalsIgnoreCase(plantEntity.getProvinceCode())) {
					address.append(region.getName()).append(" ");
				}
				if (region.getCode().equalsIgnoreCase(plantEntity.getCityCode())) {
					address.append(region.getName()).append(" ");
				}
				if (region.getCode().equalsIgnoreCase(plantEntity.getCountyCode())) {
					address.append(region.getName()).append(" ");
				}
			}
			return address.toString();
		}
		return CommonConstant.BLANK;
	}

	private void saveImportant(WifiStickPlantEntity wifiStickPlantEntity, BladeUser user, PlantEntity plant) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();

		String plantAllAddress = this.getAllAddressByPlant(plant);
		ImportantEventEntity inverterCreateImportEvent = new ImportantEventEntity();
		inverterCreateImportEvent.setPlantId(wifiStickPlantEntity.getPlantId());
		inverterCreateImportEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
		inverterCreateImportEvent.setEventContent(DeviceEventContentMultiLanguageEnum.INVERTER_EVENT_BIND_PLANT.getDeviceEventByLang(currentLanguage));
		String inverterEventBindPlantLocalizedText = DeviceEventRemarkMultiLanguageEnum.INVERTER_EVENT_BIND_PLANT.getLocalizedText(currentLanguage);
		inverterCreateImportEvent.setEventRemark(String.format(inverterEventBindPlantLocalizedText, user.getUserName(), plant.getPlantName(), plantAllAddress, wifiStickPlantEntity.getDeviceSerialNumber()));
		inverterCreateImportEvent.setSerialNumber(wifiStickPlantEntity.getDeviceSerialNumber());
		inverterCreateImportEvent.setEventDate(new Date());
		inverterCreateImportEvent.setCreateUserAccount(user.getAccount());
		inverterCreateImportEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(inverterCreateImportEvent);


		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = new DeviceExitFactoryInfoEntity();
		deviceExitFactoryInfoEntity.setDeviceSerialNumber(wifiStickPlantEntity.getDeviceSerialNumber());
		DeviceExitFactoryInfoEntity factoryInfoEntity = deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(deviceExitFactoryInfoEntity));
		ImportantEventEntity inverterExitImportEvent = new ImportantEventEntity();
		inverterExitImportEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
		inverterExitImportEvent.setPlantId(wifiStickPlantEntity.getPlantId());
		inverterExitImportEvent.setSerialNumber(factoryInfoEntity.getDeviceSerialNumber());
		inverterExitImportEvent.setEventDate(Date.from(factoryInfoEntity.getExitFactoryDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
		inverterExitImportEvent.setEventContent(DeviceEventContentMultiLanguageEnum.INVERTER_EVENT_INFO_IMPORT.getDeviceEventByLang(currentLanguage));
		String localizedText = DeviceEventRemarkMultiLanguageEnum.INVERTER_EVENT_INFO_IMPORT.getLocalizedText(currentLanguage);
		inverterExitImportEvent.setEventRemark(String.format(localizedText, factoryInfoEntity.getCompany(), factoryInfoEntity.getQualityGuaranteeYear()));
		inverterExitImportEvent.setCreateUserAccount(user.getAccount());
		importantEventEntityArrayList.add(inverterExitImportEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}

	@Override
	public AppVO inverterByWifiStickSn(AppVO appVO) {
		AppVO result = new AppVO();
		Device21Entity device21Entity = new Device21Entity();
		device21Entity.setWifiStickSerialNumber(appVO.getWifiStickSerialNumber());
		device21Entity.setPlantId(appVO.getPlantId());
		// 根据逆变器SN查询业务表，将需要扫描的电池和逆变器是否绑定, 返回逆变器型号
		Device21Entity dbDevice21Entity = device21Service.getOne(Condition.getQueryWrapper(device21Entity));
		if (ObjUtil.isEmpty(dbDevice21Entity)) {
			return result;
		}

		result.setDeviceSerialNumber(dbDevice21Entity.getDeviceSerialNumber());
		if (ValidationUtil.isNotEmpty(dbDevice21Entity.getDeviceModel())) {
			String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, dbDevice21Entity.getDeviceModel());
			appVO.setInverterKind(value);
		}
		return result;
	}

	@Override
	public boolean inverterMatch(AppVO appVO) {
		// 根据电池SN查询 电池型号，
		BatteryExitFactoryInfoEntity queryBatteryExitFactoryInfoEntity = new BatteryExitFactoryInfoEntity();
		queryBatteryExitFactoryInfoEntity.setBatterySerialNumber(appVO.getBatterySerialNumber());
		List<BatteryExitFactoryInfoEntity> dbBatteryExitFactoryInfoEntityList = batteryExitFactoryInfoService.list(Condition.getQueryWrapper(queryBatteryExitFactoryInfoEntity));
		if (CollectionUtils.isEmpty(dbBatteryExitFactoryInfoEntityList)) {
			return false;
		}
		BatteryExitFactoryInfoEntity dbBatteryExitFactoryInfoEntity = dbBatteryExitFactoryInfoEntityList.get(0);
		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntityQuery = new DeviceExitFactoryInfoEntity();
		deviceExitFactoryInfoEntityQuery.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<DeviceExitFactoryInfoEntity> list = deviceExitFactoryInfoService.list(Condition.getQueryWrapper(deviceExitFactoryInfoEntityQuery));
		if (CollectionUtils.isEmpty(list)) {
			return false;
		}
		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = list.get(0);
		String batteryType = dbBatteryExitFactoryInfoEntity.getBatteryType();
		String deviceModel = deviceExitFactoryInfoEntity.getDeviceType();
		log.info("inverterMatch device type : {}", deviceModel);
		log.info("inverterMatch battery type : {}", batteryType);
		if (this.validateBatteryTypeMatchDeviceType(batteryType, deviceModel)) {
			log.debug("not match");
			return false;
		}
		log.info("inverterMatch is true");
		return true;
	}

	private boolean validateBatteryTypeMatchDeviceType(String batteryType, String deviceModel) {
		// 电池型号匹配数据字典
		R<List<DictBiz>> deviceBatteryMatch = dictBizClient.getList("device_battery_match");
		List<DictBiz> dictData = deviceBatteryMatch.getData();
		if (CollectionUtils.isEmpty(dictData)) {
			log.info("inverterMatch dictBizClient is null");
			return true;
		}
		// 匹配电池型号 和 逆变器型号是否匹配
		List<DictBiz> matchList = dictData.stream().filter(item -> StringUtil.isNotBlank(item.getDictKey()) && StringUtil.isNotBlank(item.getDictValue()))
			.filter(item -> item.getDictKey().equalsIgnoreCase(batteryType)).filter(item -> item.getDictValue().equalsIgnoreCase(deviceModel)).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(matchList)) {
			log.info("not battery match");
			return true;
		}
		return false;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public R<String> addBatteryDevice(AppVO appVO) {
		String batterySerialNumber = appVO.getBatterySerialNumber();
		boolean redisLock = false;
		String redisKey = "battery_lock_processed:" + batterySerialNumber;
		try {
			// 给SN加锁，如果加锁成功，则SN未被扫码添加；失败该SN已经调用接口添加，直接返回空的值;
			// 不影响其他SN继续添加
			redisLock = client.tryLock(redisKey, LockType.FAIR, 0, 10, TimeUnit.SECONDS);
			if (redisLock) {
				BatteryMapDeviceEntity queryBatteryMapDevice = new BatteryMapDeviceEntity();
				queryBatteryMapDevice.setPlantId(appVO.getPlantId());
				List<BatteryMapDeviceEntity> batteryMapDeviceEntityList = batteryMapDeviceService.list(Condition.getQueryWrapper(queryBatteryMapDevice));

				R<String> r = new R<>();
				String currentLanguage = CommonUtil.getCurrentLanguage();
				List<String> batterySerialNumberList = new ArrayList<>();
				// 如果原来有添加过，则报错
				if (CollectionUtil.isNotEmpty(batteryMapDeviceEntityList)) {
					batterySerialNumberList = batteryMapDeviceEntityList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
					if (batterySerialNumberList.contains(batterySerialNumber)) {
						r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100111.autoGetMessage(currentLanguage));
						r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100111.getCode());
						return r;
					}
				}
				BatteryExitFactoryInfoEntity addBatteryExitInfo = batteryExitFactoryInfoService.getOne(Wrappers.<BatteryExitFactoryInfoEntity>query().lambda()
					.eq(BatteryExitFactoryInfoEntity::getBatterySerialNumber,
						batterySerialNumber).last("limit 1"));

				// 需求：当添加到电池为非后台的sn时，不允许添加，
				if (ObjectUtils.isEmpty(addBatteryExitInfo)) {
					r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100126.autoGetMessage(currentLanguage));
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100126.getCode());
					return r;
				}

				// 需求：：强制添加非出厂电池，并且逆变器质保期未减半过，则需要计算剩余质保期，将剩余质保期减半
				if (ObjectUtils.isEmpty(addBatteryExitInfo)) {
					// 如果非后台出厂录入的电池SN，则返回一个code错误码，
					if (ValidationUtil.isNotEmpty(appVO.getForceAddNonExitBattery()) && appVO.getForceAddNonExitBattery().equals(false)) {
						r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100124.autoGetMessage(currentLanguage));
						r.setCode(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100124.getCode());
						return r;
					} else if ((ValidationUtil.isNotEmpty(appVO.getForceAddNonExitBattery()) && appVO.getForceAddNonExitBattery().equals(true))) {
						// 如果app端接收到错误码后，仍选择继续添加(getForceAddNonExitBattery为true)，将逆变器质保期减半（生命周期内只能减半一次）
						WifiStickPlantEntity wifiStickPlantEntity = wifiStickPlantService.getOne(Wrappers.<WifiStickPlantEntity>query()
							.lambda().eq(WifiStickPlantEntity::getPlantId, appVO.getPlantId()));
						if (!ObjectUtils.isEmpty(wifiStickPlantEntity)) {
							String deviceSerialNumber = wifiStickPlantEntity.getDeviceSerialNumber();
							DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = deviceExitFactoryInfoService.getOne(Wrappers.<DeviceExitFactoryInfoEntity>query()
								.lambda().eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, deviceSerialNumber));
							String warrantyHalvedMark = deviceExitFactoryInfoEntity.getWarrantyHalvedMark();
							// 强制添加非出厂电池，并且逆变器质保期未减半过，则需要计算剩余质保期，将剩余质保期减半
							// eg：原来10年，用了一年后，剩9年，如果9年内添加了一个新的其他厂家电池，则将剩下质保期减半，为4.5年
							if (warrantyHalvedMark.equalsIgnoreCase(BizConstant.CHAR_ZERO)) {
								LocalDate nowLocalDate = LocalDate.now();
								String warrantyStartDate = deviceExitFactoryInfoEntity.getWarrantyStartDate();
								// 假设 warrantyStartDate 的格式是 yyyy-MM-dd
								DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE);
								LocalDate warrantyStartDateLocalDate = LocalDate.parse(warrantyStartDate, formatter);
								LocalDate warrantyDeadlineLocalDate = warrantyStartDateLocalDate.plusYears(Long.parseLong(deviceExitFactoryInfoEntity.getQualityGuaranteeYear()));
								long halfQuality = ChronoUnit.DAYS.between(nowLocalDate, warrantyDeadlineLocalDate) / 2;
								// 新的质保结束时间
								LocalDate plusDays = nowLocalDate.plusDays(halfQuality);
								deviceExitFactoryInfoEntity.setWarrantyDeadline(plusDays.toString());
								deviceExitFactoryInfoEntity.setWarrantyHalvedMark(BizConstant.CHAR_ONE);
								deviceExitFactoryInfoService.updateById(deviceExitFactoryInfoEntity);
							}
						}
					}
				}

				// 需求：：当已经添加了其中一类sn，则不允许添加其他的电池（即为，不允许电池混用）。只能添加相同型号的电池
				if (CollectionUtil.isNotEmpty(batteryMapDeviceEntityList)) {
					// 查询出当前电站下面已绑定电池的相关出厂信息，并获取出厂信息中的型号，如果型号和新添加电池的出厂型号不一致，则返回错误码
					List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntities = batteryExitFactoryInfoService.queryByBatterySerialNumbers(batterySerialNumberList);
					if (CollectionUtil.isNotEmpty(batteryExitFactoryInfoEntities)) {
						BatteryExitFactoryInfoEntity entity = batteryExitFactoryInfoEntities.get(0);
						String batteryType = entity.getBatteryType();
						// 去掉添加电池时，不允许不同厂商混用的限制（注意：不同型号仍需要禁止混用）
						String company = entity.getCompany();
						if (ObjectUtil.isNotEmpty(addBatteryExitInfo) && (!addBatteryExitInfo.getBatteryType().equalsIgnoreCase(batteryType)
//				 || !addBatteryExitInfo.getCompany().equalsIgnoreCase(company)
						)) {
							r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100125.autoGetMessage(currentLanguage));
							r.setCode(I18nMsgCode.SKYWORTH_CLIENT_BATTERY_100125.getCode());
							return r;
						}
					}
				}

				// 保存 电池站点关系
				BatteryMapDeviceEntity saveBatteryMapDevice = new BatteryMapDeviceEntity();
				saveBatteryMapDevice.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
				saveBatteryMapDevice.setBatterySerialNumber(batterySerialNumber);
				saveBatteryMapDevice.setPlantId(appVO.getPlantId());
				BladeUser user = AuthUtil.getUser();
				saveBatteryMapDevice.setCreateUserAccount(user.getAccount());
				saveBatteryMapDevice.setCreateUser(user.getUserId());
				if (Boolean.TRUE.equals(appVO.getMatchOrNot())) {
					saveBatteryMapDevice.setBatteryMatchDeviceFlag("true");
				} else {
					saveBatteryMapDevice.setBatteryMatchDeviceFlag("false");
				}
				batteryMapDeviceService.save(saveBatteryMapDevice);

				// 更新站点上电池设备数量
				PlantEntity updatePlantEntity = new PlantEntity();
				updatePlantEntity.setId(appVO.getPlantId());
				updatePlantEntity.setBatteryNumber(1);
				plantService.updatePlant(updatePlantEntity);
				// 保存重要事件
				this.saveBatteryImportantEventByAdd(appVO, user);

				// 将电池出厂更新为已使用
				batteryExitFactoryInfoService.updateForScanBattery(batterySerialNumber, BizConstant.NUMBER_ONE);
				return R.data("操作成功");
			}else{
				log.error("重复的接口请求");
				return R.success("");
			}
		} catch (InterruptedException e) {
			log.error("redis分布式锁加锁失败（电池）", e);
		}finally {
			if (redisLock) {
				client.unLock(redisKey, LockType.FAIR);
			}
		}
		return R.success("");
	}

	private void saveBatteryImportantEventByAdd(AppVO appVO, BladeUser user) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();
		ImportantEventEntity batteryCreateEvent = new ImportantEventEntity();
		batteryCreateEvent.setPlantId(appVO.getPlantId());
		batteryCreateEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY);
		batteryCreateEvent.setEventContent(DeviceEventContentMultiLanguageEnum.BATTERY_EVENT_BIND_BATTERY.getDeviceEventByLang(currentLanguage));
		String batteryEventBindBatteryLocalizedText = DeviceEventRemarkMultiLanguageEnum.BATTERY_EVENT_BIND_BATTERY.getLocalizedText(currentLanguage);
		batteryCreateEvent.setEventRemark(String.format(batteryEventBindBatteryLocalizedText, appVO.getBatterySerialNumber()));
		batteryCreateEvent.setSerialNumber(appVO.getBatterySerialNumber());
		batteryCreateEvent.setEventDate(new Date());
		batteryCreateEvent.setCreateUserAccount(user.getAccount());
		batteryCreateEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(batteryCreateEvent);

		// 查询当前逆变器的电池
		List<BatteryMapDeviceEntity> inverterMapBatteryList = getInverterMapBatteryList(appVO.getPlantId(),appVO.getDeviceSerialNumber());
		List<String> batterySnList = inverterMapBatteryList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
		// 查询出厂信息
		List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntities = batteryExitFactoryInfoService.queryByBatterySerialNumbers(batterySnList);
		// 计算电池总量
		double totalRatedBatteryEnergy = batteryExitFactoryInfoEntities.stream()
			.filter(x -> ObjUtil.isNotNull(x.getRatedBatteryEnergy()))
			.mapToDouble(e -> Double.parseDouble(e.getRatedBatteryEnergy()))
			.sum();
		DecimalFormat df = new DecimalFormat("#");
		ImportantEventEntity inverterAddBatteryEvent = new ImportantEventEntity();
		inverterAddBatteryEvent.setPlantId(appVO.getPlantId());
		inverterAddBatteryEvent.setSerialNumber(appVO.getDeviceSerialNumber());
		inverterAddBatteryEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
		inverterAddBatteryEvent.setEventDate(new Date());
		inverterAddBatteryEvent.setEventContent(DeviceEventContentMultiLanguageEnum.INVERTER_EVENT_BIND_BATTERY.getDeviceEventByLang(currentLanguage));
		String localizedText = DeviceEventRemarkMultiLanguageEnum.INVERTER_EVENT_BIND_BATTERY.getLocalizedText(currentLanguage);
		inverterAddBatteryEvent.setEventRemark(String.format(localizedText, appVO.getBatterySerialNumber(), batterySnList.size(), df.format(totalRatedBatteryEnergy)));
		inverterAddBatteryEvent.setCreateUserAccount(user.getAccount());
		importantEventEntityArrayList.add(inverterAddBatteryEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}

	private void saveBatteryImportantEventByDelete(BatteryMapDeviceVO batteryMapDeviceVO, BladeUser user) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();
		ImportantEventEntity batteryDeleteEvent = new ImportantEventEntity();
		batteryDeleteEvent.setPlantId(batteryMapDeviceVO.getPlantId());
		batteryDeleteEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY);
		batteryDeleteEvent.setEventContent(DeviceEventContentMultiLanguageEnum.BATTERY_EVENT_UNBIND.getDeviceEventByLang(currentLanguage));
		String batteryEventUnbindLocalizedText = DeviceEventRemarkMultiLanguageEnum.BATTERY_EVENT_UNBIND.getLocalizedText(currentLanguage);
		batteryDeleteEvent.setEventRemark(String.format(batteryEventUnbindLocalizedText, batteryMapDeviceVO.getBatterySerialNumber()));
		batteryDeleteEvent.setSerialNumber(batteryMapDeviceVO.getBatterySerialNumber());
		batteryDeleteEvent.setEventDate(new Date());
		batteryDeleteEvent.setCreateUserAccount(user.getAccount());
		batteryDeleteEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(batteryDeleteEvent);

		// 查询当前逆变器的电池
		List<BatteryMapDeviceEntity> inverterMapBatteryList = getInverterMapBatteryList(batteryMapDeviceVO.getPlantId(),batteryMapDeviceVO.getDeviceSerialNumber());
		List<String> batterySnList = inverterMapBatteryList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
		// 查询条件删除当前逆变器绑定的电池
		batterySnList.remove(batteryMapDeviceVO.getBatterySerialNumber());
		double totalRatedBatteryEnergy = 0.0;
		if (CollectionUtil.isNotEmpty(batterySnList)) {
			// 查询出厂信息
			List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntities = batteryExitFactoryInfoService.queryByBatterySerialNumbers(batterySnList);
			// 计算电池总量
			totalRatedBatteryEnergy = batteryExitFactoryInfoEntities.stream()
				.filter(x -> ValidationUtil.isNotEmpty(x.getRatedBatteryEnergy()))
				.mapToDouble(e -> Double.parseDouble(e.getRatedBatteryEnergy()))
				.sum();
		}


		DecimalFormat df = new DecimalFormat("#");
		ImportantEventEntity inverterDeleteBatteryEvent = new ImportantEventEntity();
		inverterDeleteBatteryEvent.setPlantId(batteryMapDeviceVO.getPlantId());
		inverterDeleteBatteryEvent.setSerialNumber(batteryMapDeviceVO.getDeviceSerialNumber());
		inverterDeleteBatteryEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER);
		inverterDeleteBatteryEvent.setEventDate(new Date());
		inverterDeleteBatteryEvent.setEventContent(DeviceEventContentMultiLanguageEnum.INVERTER_EVENT_UNBIND_BATTERY.getDeviceEventByLang(currentLanguage));
		String localizedText = DeviceEventRemarkMultiLanguageEnum.INVERTER_EVENT_UNBIND_BATTERY.getLocalizedText(currentLanguage);
		inverterDeleteBatteryEvent.setEventRemark(String.format(localizedText, batteryMapDeviceVO.getBatterySerialNumber(), batterySnList.size(), df.format(totalRatedBatteryEnergy)));
		inverterDeleteBatteryEvent.setCreateUserAccount(user.getAccount());
		importantEventEntityArrayList.add(inverterDeleteBatteryEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}

	private List<BatteryMapDeviceEntity> getInverterMapBatteryList(Long plantId,String deviceSerialNumber) {
		QueryWrapper<BatteryMapDeviceEntity> batteryMapDeviceVoQueryWrapper = new QueryWrapper<>();
		batteryMapDeviceVoQueryWrapper.eq(DatabaseFieldConstant.PLANT_ID, plantId)
									.eq(DatabaseFieldConstant.DEVICE_SERIAL_NUMBER, deviceSerialNumber);
		return batteryMapDeviceService.list(batteryMapDeviceVoQueryWrapper);
	}

	@Override
	public IPage<PlantVO> queryPlantBySelf(AppVO appVO, Query query) {
		PlantVO queryPlant = new PlantVO();
		query.setDescs(DatabaseFieldConstant.CREATE_TIME);
		BladeUser user = AuthUtil.getUser();
		String deptId = inspectInnerRole(user);
		queryPlant.setCreateUser(user.getUserId());
		queryPlant.setDeptId(deptId);
		IPage<PlantVO> page = Condition.getPage(query);
		// 用户类型
		String userType = StatusDisplayUtil.getRoleType(user.getRoleName(), user.getDeptId());
		IPage<PlantVO> plantVOIPage = plantService.selectPlantPage(page, queryPlant,userType,appVO.getListSearchCondition());
		List<PlantVO> plantVOList = plantVOIPage.getRecords();
		List<QueryCondition> list = new ArrayList<>();
		List<Long> plantIdList = new ArrayList<>();
		List<String> regionCodeList = new ArrayList<>();
		List<Device24Entity> device24EntityList = new ArrayList<>();
		List<WifiStickPlantEntity> wifiStickPlantEntities = new ArrayList<>();
		//获取代理商人员信息
		Map<Long, AgentUserVo> agentUserVoMap = new HashMap<>();
		getAgentUserInfo(plantVOList, agentUserVoMap);

		//获取代理商信息
		Map<Long, AgentCompanyVO> companyMap = new HashMap<>();
		getCompanyInfo(plantVOList, companyMap);

		for (PlantVO plantVO : plantVOList) {
			QueryCondition queryCondition = new QueryCondition();
			queryCondition.setPlantId(plantVO.getId());
			list.add(queryCondition);
			plantIdList.add(plantVO.getId());
			regionCodeList.add(plantVO.getCountryCode());
			regionCodeList.add(plantVO.getProvinceCode());
			regionCodeList.add(plantVO.getCityCode());
			regionCodeList.add(plantVO.getCountyCode());
			Long operationUserId = plantVO.getOperationUserId();
			Long operationCompany = plantVO.getOperationCompanyId();
			plantVO.setCompanyInfo(new AgentCompanyVO());
			plantVO.setAgentUserInfo(new AgentUserVo());
			if (ValidationUtil.isNotEmpty(operationUserId)) {
				if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(agentUserVoMap)) {
					plantVO.setAgentUserInfo(agentUserVoMap.get(operationUserId));
				} else {
					plantVO.setAgentUserInfo(new AgentUserVo());
				}

			}
			if (ValidationUtil.isNotEmpty(operationCompany)) {
				if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(companyMap)) {
					plantVO.setCompanyInfo(companyMap.get(operationCompany));
				} else {
					plantVO.setCompanyInfo(new AgentCompanyVO());
				}

			}

			Device24Entity device24Entity = new Device24Entity();
			device24Entity.setPlantId(plantVO.getId());
			device24Entity.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
			device24EntityList.add(device24Entity);

			WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
			wifiStickPlant.setPlantId(plantVO.getId());
			wifiStickPlant.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
			wifiStickPlantEntities.add(wifiStickPlant);
		}
		String currentLanguage = CommonUtil.getCurrentLanguage();
		CompletableFuture<Void> regionInfoFuture;
		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfo(regionCodeList, plantVOList));
		} else {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfoEn(regionCodeList, plantVOList));
		}

		this.getDeviceSerialNumber(plantIdList, plantVOList);

		CompletableFuture<Void> batteryDataFuture = CompletableFuture.runAsync(() -> getBatteryData(list, plantVOList));

		CompletableFuture<Void> inverterControlFuture = CompletableFuture.runAsync(() -> setInverterControl(plantVOList, device24EntityList));

		CompletableFuture<Void> startupByBackstageFuture = CompletableFuture.runAsync(() -> setStartupByBackstage(plantVOList, wifiStickPlantEntities));

		// 组合 CompletableFuture 对象并等待所有任务完成：
		CompletableFuture<Void> allFutures = CompletableFuture.allOf(regionInfoFuture, batteryDataFuture,inverterControlFuture,  startupByBackstageFuture);
		allFutures.join();


		return plantVOIPage;
	}

	/**
	 * 核查当前用户是否是内部角色
	 */
	public static String inspectInnerRole(BladeUser user) {
		String deptId = "";
		if (user.getDetail() != null) {
			Boolean roleInnerFlag = (Boolean) user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
			// 如果包含创维内部角色，则可查看所有数据，不然只能查看他自己部门的数据
			if (roleInnerFlag != null && !roleInnerFlag) {
				deptId = user.getDeptId();
			}
		}
		return deptId;
	}

	/**
	 * 获取代理商人员信息
	 */
	private void getAgentUserInfo(List<PlantVO> plantVOList, Map<Long, AgentUserVo> agentUserVoMap) {
		List<Long> userIds = plantVOList.stream().map(PlantVO::getOperationUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
		List<User> userList = userSearchClient.listByUserIds(userIds).getData();
		if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(userList)) {
			userList.parallelStream().forEach(v -> {
				AgentUserVo agentUserVo = new AgentUserVo();
				BeanUtils.copyProperties(v, agentUserVo);
				agentUserVoMap.put(v.getId(), agentUserVo);
			});
		}

	}

	/**
	 * 获取代理商公司信息
	 */
	private void getCompanyInfo(List<PlantVO> plantVOList, Map<Long, AgentCompanyVO> companyVoMap) {
		List<Long> companyIds = plantVOList.stream().map(PlantVO::getOperationCompanyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
		List<AgentCompanyVO> agentCompanyVOList = agentClient.agentCompanyInfoByIds(companyIds).getData();
		if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(agentCompanyVOList)) {
			agentCompanyVOList.parallelStream().forEach(v -> {
				AgentCompanyVO agentCompanyVO = new AgentCompanyVO();
				BeanUtils.copyProperties(v, agentCompanyVO);
				companyVoMap.put(v.getDeptId(), agentCompanyVO);
			});
		}

	}

	/**
	 * @param regionCodeList 区域code集合
	 * @param plantVOList    站点集合
	 */
	private void setRegionInfoEn(List<String> regionCodeList, List<PlantVO> plantVOList) {
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			R<List<Region>> regionResult = sysClient.getRegionList(regionCodeNotNullList);
			List<Region> regionList = regionResult.getData();
			Collections.reverse(regionList);
			for (PlantVO plantVO : plantVOList) {
				StringBuilder address = new StringBuilder(plantVO.getDetailAddress() == null ? "" : plantVO.getDetailAddress() + " ");
				if (CollectionUtils.isEmpty(regionList)) {
					break;
				}
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(plantVO.getCountyCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountyName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCityCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCityName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setProvinceName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCountryCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountryName(region.getName());
					}
				}
				plantVO.setAddress(address.toString());
			}
		}
		log.info("9-------------------------->：" + TimeUtils.getCurrentTime());
	}

	private void setStartupByBackstage(List<PlantVO> plantVOList, List<WifiStickPlantEntity> wifiStickPlantEntities) {
		if (CollectionUtils.isEmpty(plantVOList)) {
			return;
		}

		List<WifiStickPlantEntity> wifiStickInfo = wifiStickPlantService.getWifiStickInfo(wifiStickPlantEntities);
		for (PlantVO plantVO : plantVOList) {
			for (WifiStickPlantEntity wifiStickPlant : wifiStickInfo) {
				if (ValidationUtil.isNotEmpty(plantVO.getDeviceSerialNumber()) &&
					plantVO.getId().equals(wifiStickPlant.getPlantId()) &&
					plantVO.getDeviceSerialNumber().equals(wifiStickPlant.getDeviceSerialNumber())) {
					plantVO.setStartupByBackstage(wifiStickPlant.getStartupByBackstage());
					plantVO.setInverterConfigureNetwork(wifiStickPlant.getInverterConfigureNetwork());
				}
			}
		}
		log.info("8-------------------------->：" + TimeUtils.getCurrentTime());
	}

	private List<Device24Entity> setInverterControl(List<PlantVO> plantVOList, List<Device24Entity> device24EntityList) {
		if (CollectionUtils.isEmpty(plantVOList)) {
			return new ArrayList<>();
		}

		List<Device24Entity> device24Info = device24Service.getDevice24Info(device24EntityList);
		for (PlantVO plantVO : plantVOList) {
			for (Device24Entity device24 : device24Info) {
				if (plantVO.getId().equals(device24.getPlantId()) && ValidationUtil.isNotEmpty(plantVO.getDeviceSerialNumber())
					&& (plantVO.getDeviceSerialNumber().equals(device24.getDeviceSerialNumber()))) {
					plantVO.setInverterControl(device24.getInverterControl());
				}
			}
		}
//		log.info("7-------------------------->：" + TimeUtils.getCurrentTime());
		return device24Info;
	}

	private void setRegionInfo(List<String> regionCodeList, List<PlantVO> plantVOList) {
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			R<List<Region>> regionResult = sysClient.getRegionList(regionCodeNotNullList);
			List<Region> regionList = regionResult.getData();
			for (PlantVO plantVO : plantVOList) {
				StringBuilder address = new StringBuilder();
				if (CollectionUtils.isEmpty(regionList)) {
					break;
				}
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(plantVO.getCountryCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountryName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setProvinceName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCityCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCityName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(plantVO.getCountyCode())) {
						address.append(region.getName()).append(" ");
						plantVO.setCountyName(region.getName());
					}
				}
				plantVO.setAddress(address.append(" ").append(plantVO.getDetailAddress() == null ? "" : plantVO.getDetailAddress()).toString());
			}
		}
	}

	private void getBatteryData(List<QueryCondition> list, List<PlantVO> plantVOList) {
		if (CollectionUtil.isNotEmpty(list)) {
			List<BatteryCurrentStatusEntity> batteryCurrentStatusEntities = batteryCurrentStatusService.batchQueryAppBatteryCurrentStatus(list);
//			Map<Long, BatteryCurrentStatusEntity> collect = batteryCurrentStatusEntities.stream().collect(Collectors.toMap(BatteryCurrentStatusEntity::getPlantId, v -> v));
			Date now = new Date();
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			// 并机情况一下，一个站点有多个逆变器，由于app端不展示 PowerGeneration，LoadCapacity ，因此可以随机取一个，上面不能根据站点id分组，一个站点多个逆变器报错
			for (PlantVO plantVO : plantVOList) {
//				if (collect.containsKey(plantVO.getId())) {
				for(BatteryCurrentStatusEntity entity : batteryCurrentStatusEntities) {
//					BatteryCurrentStatusEntity entity = collect.get(plantVO.getId());
					if(entity.getPlantId().equals(plantVO.getId())) {
						// 判断是否为今天,不为今天，则天数据为0
						if (!formatter.format(now).equalsIgnoreCase(formatter.format(entity.getDeviceDateTime()))) {
							plantVO.setPowerGeneration(this.getChangeEnergyResult(BigDecimal.ZERO));
							plantVO.setLoadCapacity(this.getChangeEnergyResult(BigDecimal.ZERO));
							continue;
						}
						plantVO.setPowerGeneration(this.getChangeEnergyResult(entity.getTodayEnergy()));
						plantVO.setLoadCapacity(this.getChangeEnergyResult(entity.getBatteryDailyChargeEnergy()));
						plantVO.setDeviceSerialNumber(entity.getDeviceSerialNumber());
					}
				}
//				}
			}
		}
		log.info("6-------------------------->：" + TimeUtils.getCurrentTime());
	}


	private Pair<Map<Long, List<WifiStickPlantEntity>>,Map<String,String>> getDeviceSerialNumber(List<Long> plantIdList, List<PlantVO> plantVOList) {
		List<WifiStickPlantEntity> wifiStickPlantVOList = new ArrayList<>();
		Map<String, String> inverterKindMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(plantIdList)) {
			wifiStickPlantVOList = wifiStickPlantService.queryDeviceSerialNumberList(plantIdList);
			List<String> serialNumberList = new ArrayList<>();
			for (WifiStickPlantEntity wifiStickPlantEntity : wifiStickPlantVOList) {
				serialNumberList.add(wifiStickPlantEntity.getDeviceSerialNumber());
				for (PlantVO plantVO : plantVOList) {
					if (ValidationUtil.isEmpty(plantVO.getDeviceSerialNumber()) && plantVO.getId().equals(wifiStickPlantEntity.getPlantId())) {
						// 获取第一台添加的逆变器的sn
						plantVO.setDeviceSerialNumber(wifiStickPlantEntity.getDeviceSerialNumber());
					}
				}
			}
//			List<Device23Entity> device23List = device23Service.selectDevice23ListBySnList(serialNumberList);
			List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntityList = deviceExitFactoryInfoService.getListByDeviceSerialNumberCollect(serialNumberList);
			Map<String, String> deviceExitCollect = deviceExitFactoryInfoEntityList.stream().collect(Collectors.toMap(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getDeviceType));
//			Map<String, String> parallelModeCollect = device23List.stream().collect(Collectors.toMap(Device23Entity::getDeviceSerialNumber, Device23Entity::getParallelModeFunction));
			// 兼容旧版 app
			for (PlantVO plantVO : plantVOList) {
				if (deviceExitCollect.containsKey(plantVO.getDeviceSerialNumber())) {
					String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, deviceExitCollect.get(plantVO.getDeviceSerialNumber()));
					plantVO.setInverterKind(value);
				}
			}

			for (DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity : deviceExitFactoryInfoEntityList) {
				String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, deviceExitCollect.get(deviceExitFactoryInfoEntity.getDeviceSerialNumber()));
				inverterKindMap.put(deviceExitFactoryInfoEntity.getDeviceSerialNumber(), value);
			}

		}
		log.info("5-------------------------->：" + TimeUtils.getCurrentTime());
		// 对站点下所有设备进行分组
		return Pair.of(wifiStickPlantVOList.stream()
				.collect(Collectors.groupingBy(WifiStickPlantEntity::getPlantId,Collectors.toList())),inverterKindMap);
	}

	private void subtractBeforeData(List<AppReportDataVO> list) {
		BigDecimal beforeCharge = new BigDecimal(0);
		for (AppReportDataVO vo : list) {
			BigDecimal value = vo.getValue();
			if (value.doubleValue() != 0) {
				if (beforeCharge.compareTo(value) > 0) {
					continue;
				}
				BigDecimal result = value.subtract(beforeCharge);
				vo.setValue(result);
				beforeCharge = value;
			}
		}
	}


//	@Override
//	public AppReportHeaderVO queryPlantRunningState(AppVO appVO) {
//		AppReportHeaderVO resultAppReportHeaderVO = this.setReportHeaderData(appVO);
//		try {
//			// 查询日月年报表数据
//			if (0 == appVO.getType()) {
//				AppReportDetailVO hourReport = this.getHoursReport(appVO);
//				List<AppReportDataVO> powerGeneration = hourReport.getPowerGeneration();
//				List<AppReportDataVO> dischargeCapacity = hourReport.getDischargeCapacity();
//				this.sortList(powerGeneration);
//				this.sortList(dischargeCapacity);
//				this.subtractBeforeData(powerGeneration);
//				this.subtractBeforeData(dischargeCapacity);
//				resultAppReportHeaderVO.setDailyReport(hourReport);
//			} else if (1 == appVO.getType()) {
//				// 周报表，过去7天
//				SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.PATTERN_DATE);
//				Date parse = simpleDateFormat.parse(appVO.getDataScope());
//				Instant instant = parse.toInstant();
//				LocalDate endLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
//				LocalDate beginLocalDate = endLocalDate.plusDays(-7L);
//				Function<QueryCondition, List<BatteryEverydayTotalVO>> dayFun = batteryEverydayTotalService::weekEstimate;
//				AppReportDetailVO dailyReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, dayFun);
//				this.completionDay(dailyReport, beginLocalDate, endLocalDate, "MM-dd");
//				resultAppReportHeaderVO.setWeekReport(dailyReport);
//			} else if (2 == appVO.getType()) {
//				// 月报表
//				SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
//				Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
//				Instant instant = dataScope.toInstant();
//				LocalDate month = instant.atZone(ZoneId.systemDefault()).toLocalDate();
//				LocalDate beginLocalDate = month.with(TemporalAdjusters.firstDayOfMonth());
//				LocalDate endLocalDate = month.with(TemporalAdjusters.lastDayOfMonth());
//				Function<QueryCondition, List<BatteryEverydayTotalVO>> monthFun = batteryEverydayTotalService::monthEstimate;
//				AppReportDetailVO monthReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, monthFun);
//				this.completionDay(monthReport, beginLocalDate, endLocalDate, "dd");
//				resultAppReportHeaderVO.setMonthlyReport(monthReport);
//			} else if (3 == appVO.getType()) {
//				// 年报表
//				SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
//				Date dataScope = simpleDateFormat.parse(appVO.getDataScope());
//				Instant instant = dataScope.toInstant();
//				LocalDate year = instant.atZone(ZoneId.systemDefault()).toLocalDate();
//				LocalDate endLocalDate = year.with(TemporalAdjusters.lastDayOfYear());
//				LocalDate beginLocalDate = year.with(TemporalAdjusters.firstDayOfYear());
//				Function<QueryCondition, List<BatteryEverydayTotalVO>> annualFun = batteryEverydayTotalService::annualEstimate;
//				AppReportDetailVO annualReport = this.getAppReportDetailVO(appVO, beginLocalDate, endLocalDate, simpleDateFormat, annualFun);
//				this.completionMonth(annualReport);
//				resultAppReportHeaderVO.setAnnualReport(annualReport);
//			}
//		} catch (ParseException e) {
//			log.error("error ParseException :{} ", e);
//		}
//		return resultAppReportHeaderVO;
//	}


	@Override
	public AppReportHeaderVO queryPlantRunningStateHeaderV2(AppVO appVO) {
		return appReportServiceImpl.queryPlantRunningStateHeaderV2(appVO);
	}

	@Override
	public AppReportHeaderVO queryRunningStateChartV2(AppVO appVO) {
		return appReportServiceImpl.queryRunningStateChartV2(appVO);
	}

	@Override
	public PlantDetailVO getPlantDetail(AppVO appVO) {
		PlantVO plantVO = new PlantVO();
		PlantDetailVO plantDetailVO = new PlantDetailVO();
		Long plantId = appVO.getPlantId();
		plantVO.setId(plantId);
		PlantEntity one = plantService.getOne(Condition.getQueryWrapper(plantVO));
		BeanUtils.copyProperties(one, plantVO);
		List<PlantVO> plantVOList = Collections.singletonList(plantVO);
		//获取代理商人员信息
		Map<Long, AgentUserVo> agentUserVoMap = new HashMap<>();
		getAgentUserInfo(plantVOList, agentUserVoMap);

		//获取代理商信息
		Map<Long, AgentCompanyVO> companyMap = new HashMap<>();
		getCompanyInfo(plantVOList, companyMap);


		List<String> regionCodeList = new ArrayList<>();
		List<Long> plantIdList = new ArrayList<>();
		List<QueryCondition> list = new ArrayList<>();
		List<Device24Entity> device24EntityList = new ArrayList<>();
		List<WifiStickPlantEntity> wifiStickPlantEntities = new ArrayList<>();
		PlantVO regionCodePlant = plantVOList.get(0);
		regionCodeList.add(regionCodePlant.getCountryCode());
		regionCodeList.add(regionCodePlant.getProvinceCode());
		regionCodeList.add(regionCodePlant.getCityCode());
		regionCodeList.add(regionCodePlant.getCountyCode());
		plantIdList.add(appVO.getPlantId());
		QueryCondition queryCondition = new QueryCondition();
		queryCondition.setPlantId(appVO.getPlantId());
		list.add(queryCondition);
		Device24Entity device24Entity = new Device24Entity();
		device24Entity.setPlantId(plantVO.getId());
		device24Entity.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
		device24EntityList.add(device24Entity);

		WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
		wifiStickPlant.setPlantId(plantVO.getId());
		wifiStickPlant.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
		wifiStickPlantEntities.add(wifiStickPlant);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		CompletableFuture<Void> regionInfoFuture;

		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfo(regionCodeList, plantVOList));
		} else {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfoEn(regionCodeList, plantVOList));
		}

		Pair<Map<Long, List<WifiStickPlantEntity>>,Map<String,String>> pairMap = this.getDeviceSerialNumber(plantIdList, plantVOList);

		CompletableFuture<Void> batteryDataFuture = CompletableFuture.runAsync(() -> getBatteryData(list, plantVOList));

//		CompletableFuture<Void> inverterControlFuture = CompletableFuture.runAsync(() -> setInverterControl(plantVOList, device24EntityList));
		List<Device24Entity> dbResultDevice24EntityList = this.setInverterControl(plantVOList, device24EntityList);
		CompletableFuture<Void> startupByBackstageFuture = CompletableFuture.runAsync(() -> setStartupByBackstage(plantVOList, wifiStickPlantEntities));

		PlantVO agentPlant = plantVOList.get(0);
		Map<Long, List<WifiStickPlantEntity>> groupPlantIdMap = pairMap.getLeft();
		Map<String,String> deviceNumberInverterKindMap = pairMap.getRight();
		List<WifiStickPlantEntity> plantDeviceList = groupPlantIdMap.get(agentPlant.getId());

		Map<Long, List<Device24Entity>> groupPlantIdDevice24Map = new HashMap<>();
		if(CollectionUtil.isNotEmpty(dbResultDevice24EntityList)) {
			groupPlantIdDevice24Map = dbResultDevice24EntityList.stream()
					.collect(Collectors.groupingBy(Device24Entity::getPlantId, Collectors.toList()));
		}
		List<Device24Entity> plantIdDevice24List = groupPlantIdDevice24Map.get(agentPlant.getId());
		List<PlantDeviceVO> plantDevice = this.getPlantDevice(plantDeviceList,plantIdDevice24List,deviceNumberInverterKindMap);
		Long operationUserId = agentPlant.getOperationUserId();
		Long operationCompany = agentPlant.getOperationCompanyId();
		if (ValidationUtil.isNotEmpty(operationUserId)) {
			if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(agentUserVoMap)) {
				plantVO.setAgentUserInfo(agentUserVoMap.get(operationUserId));
			} else {
				plantVO.setAgentUserInfo(new AgentUserVo());
			}

		}
		if (ValidationUtil.isNotEmpty(operationCompany)) {
			if (!org.springblade.common.utils.CollectionUtils.isNullOrEmpty(companyMap)) {
				plantVO.setCompanyInfo(companyMap.get(operationCompany));
			} else {
				plantVO.setCompanyInfo(new AgentCompanyVO());
			}
		}

		// 组合 CompletableFuture 对象并等待所有任务完成：
		CompletableFuture<Void> allFutures = CompletableFuture.allOf(regionInfoFuture, batteryDataFuture,  startupByBackstageFuture);
		allFutures.join();

		BeanUtils.copyProperties(plantVO, plantDetailVO);
		// 获取站点设备信息，包含并机信息
		plantDetailVO.setPlantDeviceVOList(plantDevice);
		return plantDetailVO;
	}

	@Override
	public IPage<PlantListVO> queryPlantBySelfV2(AppVO appVO, Query query) {
		log.info("1-------------------------->：" + TimeUtils.getCurrentTime());
		BladeUser user = AuthUtil.getUser();
		String deptId = inspectInnerRole(user);
		log.info("2-------------------------->：" + TimeUtils.getCurrentTime());
		query.setDescs(DatabaseFieldConstant.CREATE_TIME);
		PlantVO queryPlant = new PlantVO();
		queryPlant.setCreateUser(user.getUserId());
		queryPlant.setDeptId(deptId);
		queryPlant.setListSearchCondition(appVO.getListSearchCondition());
		log.info("2.1-------------------------->：" + TimeUtils.getCurrentTime());
		IPage<PlantVO> page = Condition.getPage(query);
		// 用户类型
		String userType = StatusDisplayUtil.getRoleType(user.getRoleName(), user.getDeptId());
		IPage<PlantVO> plantVOIPage = plantService.selectPlantPage(page, queryPlant, userType, appVO.getListSearchCondition());
		log.info("2.2-------------------------->：" + TimeUtils.getCurrentTime());
		if (CollectionUtils.isEmpty(plantVOIPage.getRecords())) {
			return new Page<>();
		}
		List<PlantVO> plantVOList = plantVOIPage.getRecords();
		log.info("3-------------------------->：" + TimeUtils.getCurrentTime());

		List<Long> plantIdList = new ArrayList<>();
		List<String> regionCodeList = new ArrayList<>();
		List<PlantListVO> plantListVOArrayList = new ArrayList<>();
		List<Device24Entity> device24EntityList = new ArrayList<>();
		List<WifiStickPlantEntity> wifiStickPlantEntities = new ArrayList<>();
		List<QueryCondition> batteryDataQueryList = new ArrayList<>();

		for (PlantVO plantVO : plantVOList) {
			QueryCondition queryCondition = new QueryCondition();
			queryCondition.setPlantId(plantVO.getId());
			plantIdList.add(plantVO.getId());
			regionCodeList.addAll(Arrays.asList(plantVO.getCountryCode(), plantVO.getProvinceCode(),
				plantVO.getCityCode(), plantVO.getCountyCode()));
			batteryDataQueryList.add(queryCondition);

			Device24Entity device24Entity = new Device24Entity();
			device24Entity.setPlantId(plantVO.getId());
			device24Entity.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
			device24EntityList.add(device24Entity);

			WifiStickPlantEntity wifiStickPlant = new WifiStickPlantEntity();
			wifiStickPlant.setPlantId(plantVO.getId());
			wifiStickPlant.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
			wifiStickPlantEntities.add(wifiStickPlant);
		}
		log.info("4-------------------------->：" + TimeUtils.getCurrentTime());
		Pair<Map<Long, List<WifiStickPlantEntity>>,Map<String,String>> pairMap = this.getDeviceSerialNumber(plantIdList, plantVOList);

		CompletableFuture<Void> batteryDataFuture = CompletableFuture.runAsync(() -> getBatteryData(batteryDataQueryList, plantVOList), customExecutor);

//		CompletableFuture<Void> inverterControlFuture = CompletableFuture.runAsync(() -> setInverterControl(plantVOList, device24EntityList), customExecutor);
		List<Device24Entity> dbResultDevice24EntityList = this.setInverterControl(plantVOList, device24EntityList);
		CompletableFuture<Void> startupByBackstageFuture = CompletableFuture.runAsync(() -> setStartupByBackstage(plantVOList, wifiStickPlantEntities), customExecutor);

		String currentLanguage = CommonUtil.getCurrentLanguage();
		CompletableFuture<Void> regionInfoFuture;
		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfo(regionCodeList, plantVOList), customExecutor);
		} else {
			regionInfoFuture = CompletableFuture.runAsync(() -> setRegionInfoEn(regionCodeList, plantVOList), customExecutor);
		}
		// 组合 CompletableFuture 对象并等待所有任务完成：
		CompletableFuture<Void> allFutures = CompletableFuture.allOf(regionInfoFuture, batteryDataFuture,  startupByBackstageFuture);//inverterControlFuture
		allFutures.join();
		log.info("10-------------------------->：" + TimeUtils.getCurrentTime());
		Map<Long, List<Device24Entity>> groupPlantIdDevice24Map = new HashMap<>();
		if(CollectionUtil.isNotEmpty(dbResultDevice24EntityList)) {
			groupPlantIdDevice24Map = dbResultDevice24EntityList.stream()
					.collect(Collectors.groupingBy(Device24Entity::getPlantId, Collectors.toList()));
		}
		Map<Long, List<WifiStickPlantEntity>> groupPlantIdMap = pairMap.getLeft();
		Map<String,String> deviceNumberInverterKindMap = pairMap.getRight();
		for (PlantVO plantVO : plantVOList) {
			PlantListVO plantListVO = new PlantListVO();
			this.copyValue(plantVO, plantListVO);
			List<WifiStickPlantEntity> plantDeviceList = groupPlantIdMap.get(plantVO.getId());
			List<Device24Entity> plantIdDevice24List = groupPlantIdDevice24Map.get(plantVO.getId());
			List<PlantDeviceVO> plantDevice = this.getPlantDevice(plantDeviceList,plantIdDevice24List,deviceNumberInverterKindMap);
			// 获取站点设备信息，包含并机信息
			plantListVO.setPlantDeviceVOList(plantDevice);
			plantListVOArrayList.add(plantListVO);
		}

		log.info("11-------------------------->：" + TimeUtils.getCurrentTime());
		IPage<PlantListVO> plantListVOIPage = new Page<>();
		plantListVOIPage.setPages(plantVOIPage.getPages());
		plantListVOIPage.setSize(plantVOIPage.getSize());
		plantListVOIPage.setCurrent(plantVOIPage.getCurrent());
		plantListVOIPage.setTotal(plantVOIPage.getTotal());
		// 如果集合大小与数据库查询的数据量不一致，则将总页数等参数重新设置
		//if (plantListVOArrayList.size() != plantVOIPage.getSize()){
		//	plantListVOIPage.setPages(1L);
		//	plantListVOIPage.setSize(plantListVOArrayList.size());
		//	plantListVOIPage.setCurrent(1L);
		//	plantListVOIPage.setTotal(plantListVOArrayList.size());
		//}
		plantListVOIPage.setRecords(plantListVOArrayList);

		log.info("12-------------------------->：" + TimeUtils.getCurrentTime());
		return plantListVOIPage;
	}

	private List<PlantDeviceVO> getPlantDevice(List<WifiStickPlantEntity> plantDeviceList,List<Device24Entity> plantIdDevice24List,
											   Map<String,String> deviceNumberInverterKindMap) {
		if(plantDeviceList == null || plantDeviceList.isEmpty()){
			return new ArrayList<>();
		}
		List<PlantDeviceVO> deviceList = new ArrayList<>();
		for(WifiStickPlantEntity entity : plantDeviceList){
			PlantDeviceVO plantDeviceVO = new PlantDeviceVO();
			plantDeviceVO.setPlantId(entity.getPlantId());
			plantDeviceVO.setDeviceSerialNumber(entity.getDeviceSerialNumber());
			plantDeviceVO.setParallelModeFunction(entity.getParallelModeFunction() == null ? "0" : entity.getParallelModeFunction());
			plantDeviceVO.setCreateTime(entity.getCreateTime());
			plantDeviceVO.setStartupByBackstage(entity.getStartupByBackstage());
			plantDeviceVO.setInverterConfigureNetwork(entity.getInverterConfigureNetwork());
			plantDeviceVO.setOnLineStatus(entity.getOnLineStatus());
			if(CollectionUtil.isNotEmpty(plantIdDevice24List)) {
				List<String> device24List = plantIdDevice24List.stream()
					.filter(p -> entity.getDeviceSerialNumber().equals(p.getDeviceSerialNumber())).map(Device24Entity::getInverterControl).collect(Collectors.toList());
				if(CollectionUtil.isNotEmpty(device24List)) {
					plantDeviceVO.setInverterControl(device24List.get(0));
				}
			}
			plantDeviceVO.setInverterKind(deviceNumberInverterKindMap.get(entity.getDeviceSerialNumber()));
			deviceList.add(plantDeviceVO);
		}
		// 对每个分组中的元素按照创建时间降序排列
//			Collections.sort(deviceList, (e1, e2) -> e2.getCreateTime().compareTo(e1.getCreateTime()));
		deviceList.sort((e1, e2) -> e1.getCreateTime().compareTo(e2.getCreateTime()));
		return deviceList;
	}

	private void copyValue(PlantVO plantVO, PlantListVO plantListVO) {
		plantListVO.setPlantName(plantVO.getPlantName());
		plantListVO.setCountryCode(plantVO.getCountryCode());
		plantListVO.setProvinceCode(plantVO.getProvinceCode());
		plantListVO.setCityCode(plantVO.getCityCode());
		plantListVO.setCountyCode(plantVO.getCountyCode());
		plantListVO.setCountryName(plantVO.getCountryName());
		plantListVO.setProvinceName(plantVO.getProvinceName());
		plantListVO.setCityName(plantVO.getCityName());
		plantListVO.setCountyName(plantVO.getCountyName());
		plantListVO.setTimeZone(plantVO.getTimeZone());
		plantListVO.setDetailAddress(plantVO.getDetailAddress());
		// 状态转换
		plantListVO.setPlantStatus(plantVO.getPlantStatus());
		plantListVO.setPowerGeneration(plantVO.getPowerGeneration());
		plantListVO.setLoadCapacity(plantVO.getLoadCapacity());
		plantListVO.setDeviceSerialNumber(plantVO.getDeviceSerialNumber());
		plantListVO.setAddress(plantVO.getAddress());
		plantListVO.setInverterKind(plantVO.getInverterKind());
		plantListVO.setCreateUserAccount(plantVO.getCreateUserAccount());
		plantListVO.setUpdateUserAccount(plantVO.getUpdateUserAccount());
		plantListVO.setId(plantVO.getId());
		plantListVO.setCreateTime(plantVO.getCreateTime());
		plantListVO.setUpdateTime(plantVO.getUpdateTime());

		plantListVO.setInverterControl(plantVO.getInverterControl());
		plantListVO.setStartupByBackstage(plantVO.getStartupByBackstage());
		plantListVO.setInverterConfigureNetwork(plantVO.getInverterConfigureNetwork());
		plantListVO.setIsParallelMode(plantVO.getIsParallelMode());

	}


	private void completionDay(AppReportDetailVO report, LocalDate beginLocalDate, LocalDate endLocalDate, String format) {
		List<AppReportDataVO> dischargeCapacityList = report.getDischargeCapacity();
		int interval;
		if ("dd".equals(format)) {
			interval = beginLocalDate.lengthOfMonth();
		} else {
			long epochDay = endLocalDate.toEpochDay() - beginLocalDate.toEpochDay();
			interval = Long.valueOf(epochDay).intValue();
		}
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
		Map<String, BigDecimal> initDaysMap = this.initDaysMap(dateTimeFormatter, interval, beginLocalDate);
		List<AppReportDataVO> resultList = this.setDbToInitMap(initDaysMap, dischargeCapacityList);
		report.setDischargeCapacity(resultList);
		List<AppReportDataVO> powerGenerationList = report.getPowerGeneration();
		report.setPowerGeneration(this.setDbToInitMap(initDaysMap, powerGenerationList));
	}

	private void completionMonth(AppReportDetailVO report) {
		List<AppReportDataVO> dischargeCapacityList = report.getDischargeCapacity();
		Map<String, BigDecimal> initDaysMap = this.initMonthsMap();
		List<AppReportDataVO> resultList = this.getNotExistKeyList(initDaysMap, dischargeCapacityList);
		dischargeCapacityList.addAll(resultList);
		List<AppReportDataVO> powerGenerationList = report.getPowerGeneration();
		powerGenerationList.addAll(this.getNotExistKeyList(initDaysMap, powerGenerationList));
		this.sortList(dischargeCapacityList);
		this.sortList(powerGenerationList);
	}

	private Map<String, BigDecimal> initMonthsMap() {
		Map<String, BigDecimal> initMonthsMap = new HashMap<>();
		BigDecimal zero = new BigDecimal("0.000");
		initMonthsMap.put("01", zero);
		initMonthsMap.put("02", zero);
		initMonthsMap.put("03", zero);
		initMonthsMap.put("04", zero);
		initMonthsMap.put("05", zero);
		initMonthsMap.put("06", zero);
		initMonthsMap.put("07", zero);
		initMonthsMap.put("08", zero);
		initMonthsMap.put("09", zero);
		initMonthsMap.put("10", zero);
		initMonthsMap.put("11", zero);
		initMonthsMap.put("12", zero);
		return initMonthsMap;
	}

	@NotNull
	private List<AppReportDataVO> getNotExistKeyList(Map<String, BigDecimal> initDaysMap, List<AppReportDataVO> dischargeCapacityList) {
		List<AppReportDataVO> resultList = new ArrayList<>();
		for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
			String key = entry.getKey();
			BigDecimal value = entry.getValue();
			boolean existKey = false;
			for (AppReportDataVO vo : dischargeCapacityList) {
				if (key.equals(vo.getKey())) {
					existKey = true;
					break;
				}
			}
			if (!existKey) {
				AppReportDataVO addVo = new AppReportDataVO();
				addVo.setKey(key);
				addVo.setValue(value);
				resultList.add(addVo);
			}
		}
		return resultList;
	}

	private List<AppReportDataVO> setDbToInitMap(Map<String, BigDecimal> initDaysMap, List<AppReportDataVO> dischargeCapacityList) {
		List<AppReportDataVO> resultList = new ArrayList<>();
		for (AppReportDataVO vo : dischargeCapacityList) {
			for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
				String key = entry.getKey();
				if (key.equals(vo.getKey())) {
					entry.setValue(vo.getValue());
					break;
				}
			}
		}
		for (Map.Entry<String, BigDecimal> entry : initDaysMap.entrySet()) {
			String key = entry.getKey();
			BigDecimal value = entry.getValue();
			AppReportDataVO addVo = new AppReportDataVO();
			addVo.setKey(key);
			addVo.setValue(value);
			resultList.add(addVo);
		}

		return resultList;
	}

	private Map<String, BigDecimal> initDaysMap(DateTimeFormatter dateTimeFormatter, int intervalDay, LocalDate beginLocalDate) {
		Map<String, BigDecimal> initDaysMap = new LinkedHashMap<>();
		BigDecimal zero = new BigDecimal("0.000");
		for (int i = 0; i < intervalDay; i++) {
			LocalDate localDate = beginLocalDate.plusDays(Integer.toUnsignedLong(i));
			String formatDay = dateTimeFormatter.format(localDate);
			initDaysMap.put(formatDay, zero);
		}
		log.info("initDaysMap : {}", initDaysMap);
		return initDaysMap;
	}


	private AppReportDetailVO getHoursReport(AppVO appVO) throws ParseException {
		// 天报表，每4小时累计统计
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.PATTERN_DATE);
		Date begin = simpleDateFormat.parse(appVO.getDataScope());//" 00:00:00"
		QueryDeviceLog22Condition query = new QueryDeviceLog22Condition();
		query.setStartDateTime(begin);
		query.setPlantId(appVO.getPlantId());
		query.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<DeviceLog22VO> deviceLog22VoList = deviceLog22ByDorisService.appReportEstimate(query);
		Map<String, BigDecimal> pvDailyChargeMap = this.initHoursMap();
		// 当日总充电量
		Map<String, BigDecimal> chargeMap = this.initHoursMap();
		for (DeviceLog22VO deviceLog22VO : deviceLog22VoList) {
			String appTotalDate = deviceLog22VO.getAppTotalDate();
			BigDecimal appPvTodayEnergyTemp = deviceLog22VO.getPvTodayEnergy();
			if (appPvTodayEnergyTemp == null) {
				appPvTodayEnergyTemp = new BigDecimal(0);
			}
			BigDecimal appBatteryDailyAccumulatedChargeEnergyTemp = deviceLog22VO.getAppBatteryAccumulatedChargeEnergy();
			if (appBatteryDailyAccumulatedChargeEnergyTemp == null) {
				appBatteryDailyAccumulatedChargeEnergyTemp = new BigDecimal(0);
			}
			BigDecimal thoundsandAppPvTodayEnergyEnergy = appPvTodayEnergyTemp.divide(new BigDecimal(1000), 4, RoundingMode.HALF_UP);
			BigDecimal thoundsandAppBatteryAccumulatedChargeEnergy = appBatteryDailyAccumulatedChargeEnergyTemp.divide(new BigDecimal(1000), 4, RoundingMode.HALF_UP);
			if (appTotalDate.compareTo("00:00") > 0 && appTotalDate.compareTo("04:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "04:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "04:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("04:00") > 0 && appTotalDate.compareTo("08:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "08:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "08:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("08:00") > 0 && appTotalDate.compareTo("12:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "12:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "12:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("12:00") > 0 && appTotalDate.compareTo("16:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "16:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "16:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("16:00") > 0 && appTotalDate.compareTo("20:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "20:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "20:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			} else if (appTotalDate.compareTo("20:00") > 0 && appTotalDate.compareTo("24:00") <= 0) {
				this.addTimeValue(pvDailyChargeMap, "24:00", thoundsandAppPvTodayEnergyEnergy);
				this.addTimeValue(chargeMap, "24:00", thoundsandAppBatteryAccumulatedChargeEnergy);
			}
		}
		return this.getAppReportDetailVO(pvDailyChargeMap, chargeMap);
	}

	private Map<String, BigDecimal> initHoursMap() {
		Map<String, BigDecimal> chargeMap = new HashMap<>();
		BigDecimal zero = new BigDecimal("0.000");
		chargeMap.put("00:00", zero);
		chargeMap.put("04:00", zero);
		chargeMap.put("08:00", zero);
		chargeMap.put("12:00", zero);
		chargeMap.put("16:00", zero);
		chargeMap.put("20:00", zero);
		chargeMap.put("24:00", zero);
		return chargeMap;
	}

	@NotNull
	private AppReportDetailVO getAppReportDetailVO(Map<String, BigDecimal> pvDailyChargeMap, Map<String, BigDecimal> chargeMap) {
		AppReportDetailVO hourReport = new AppReportDetailVO();
		List<AppReportDataVO> powerGeneration = new ArrayList<>();
		List<AppReportDataVO> pvDailyChargeCapacity = new ArrayList<>();
		for (Map.Entry<String, BigDecimal> entry : pvDailyChargeMap.entrySet()) {
			// 光伏日发电量
			AppReportDataVO pvDailyCharge = new AppReportDataVO();
			pvDailyCharge.setKey(entry.getKey());
			pvDailyCharge.setValue(entry.getValue());
			pvDailyChargeCapacity.add(pvDailyCharge);
		}
		for (Map.Entry<String, BigDecimal> entry : chargeMap.entrySet()) {
			// 总充电量
			AppReportDataVO dailyPower = new AppReportDataVO();
			dailyPower.setKey(entry.getKey());
			dailyPower.setValue(entry.getValue());
			powerGeneration.add(dailyPower);
		}

		hourReport.setPowerGeneration(pvDailyChargeCapacity);
		hourReport.setDischargeCapacity(powerGeneration);
		return hourReport;
	}

	private void sortList(List<AppReportDataVO> list) {
		if (list != null) {
			list.sort(Comparator.comparing(AppReportDataVO::getKey));
		}
	}

	private void addTimeValue(Map<String, BigDecimal> map, String timeKey, BigDecimal addNumber) {
		BigDecimal timeValue = map.get(timeKey);
		if (timeValue == null) {
			timeValue = new BigDecimal(0);
		}
		BigDecimal addResult = timeValue.add(addNumber);
		map.put(timeKey, addResult);
	}

	private AppReportHeaderVO setReportHeaderData(AppVO appVO) {
		AppReportHeaderVO resultAppReportHeaderVO = new AppReportHeaderVO();
		// 查询报表头部数据
		this.getBatteryData(appVO, resultAppReportHeaderVO);
		//查询电网数据
		this.getDeviceImportData(appVO, resultAppReportHeaderVO);

		return resultAppReportHeaderVO;
	}

	private void getBatteryData(AppVO appVO, AppReportHeaderVO resultAppReportHeaderVO) {
		BatteryCurrentStatusEntity queryBatteryCurrentStatusEntity = new BatteryCurrentStatusEntity();
		queryBatteryCurrentStatusEntity.setPlantId(appVO.getPlantId());
		queryBatteryCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<BatteryCurrentStatusEntity> dbBatteryCurrentStatuList = batteryCurrentStatusService.list(Condition.getQueryWrapper(queryBatteryCurrentStatusEntity));
		if (CollectionUtil.isNotEmpty(dbBatteryCurrentStatuList)) {
			BatteryCurrentStatusEntity dbBatteryCurrentStatus = dbBatteryCurrentStatuList.get(0);
			log.info("before getBatteryData deviceSerialNumber : {}", appVO.getDeviceSerialNumber());
			// 数据库中为瓦
			BigDecimal todayEnergyTemp = dbBatteryCurrentStatus.getTodayEnergy();
			log.info("before getBatteryData todayEnergy : {}", todayEnergyTemp);
			resultAppReportHeaderVO.setTodayEnergy(this.getChangeEnergyResult(todayEnergyTemp));
			BigDecimal tatal = dbBatteryCurrentStatus.getTotalEnergy() == null ? new BigDecimal(0) : dbBatteryCurrentStatus.getTotalEnergy();
			BigDecimal multiplyTotal = tatal.multiply(BizConstant.THOUSAND);
			log.info("before getBatteryData TotalEnergy : {}", multiplyTotal);
			// 数据库中本来为千瓦
			resultAppReportHeaderVO.setTotalEnergy(this.getChangeEnergyResult(multiplyTotal));
			BigDecimal batteryDailyDischargeEnergy = dbBatteryCurrentStatus.getBatteryDailyDischargeEnergy();
			log.info("before getBatteryData batteryDailyDischarge : {}", batteryDailyDischargeEnergy);
			resultAppReportHeaderVO.setBatteryTodayDischargeEnergy(this.getChangeEnergyResult(batteryDailyDischargeEnergy));
			BigDecimal batteryTotalDischargeEnergy = dbBatteryCurrentStatus.getBatteryAccumulatedDischargeEnergy();
			log.info("before getBatteryData batteryTotalDischarge : {}", batteryTotalDischargeEnergy);
			resultAppReportHeaderVO.setBatteryAccumulatedDischargeEnergy(this.getChangeEnergyResult(batteryTotalDischargeEnergy));
			SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.PATTERN_DATE);
			Date deviceDateTime = dbBatteryCurrentStatus.getDeviceDateTime();
			Date now = new Date();
			// 判断是否为今天,不为今天，则天数据为0
			if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
				resultAppReportHeaderVO.setTodayEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
				resultAppReportHeaderVO.setBatteryTodayDischargeEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
			}
		}
	}

	private String getChangeEnergyResult(BigDecimal bigDecimal) {
		BigDecimal temp = bigDecimal == null ? new BigDecimal(0) : bigDecimal;
		String result;
		if (temp.compareTo(BizConstant.THOUSAND) > 0) {
			result = temp.divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP) + BizConstant.UNIT_THOUSAND_WH;
		} else {
			result = temp.setScale(1, RoundingMode.HALF_UP) + BizConstant.UNIT_WH;
		}
		log.info("after getBatteryData result : {}", result);
		return result;
	}

	private void getDeviceImportData(AppVO appVO, AppReportHeaderVO resultAppReportHeaderVO) {
		DeviceCurrentStatusEntity queryDeviceCurrentStatusEntity = new DeviceCurrentStatusEntity();
		queryDeviceCurrentStatusEntity.setPlantId(appVO.getPlantId());
		queryDeviceCurrentStatusEntity.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<DeviceCurrentStatusEntity> dbDeviceCurrentStatusList = deviceCurrentStatusService.list(Condition.getQueryWrapper(queryDeviceCurrentStatusEntity));
		if (CollectionUtil.isNotEmpty(dbDeviceCurrentStatusList)) {
			DeviceCurrentStatusEntity dbDeviceCurrentStatus = dbDeviceCurrentStatusList.get(0);
			BigDecimal todayImportEnergy = dbDeviceCurrentStatus.getTodayImportEnergy();
			BigDecimal accumulatedEnergyOfPositive = dbDeviceCurrentStatus.getAccumulatedEnergyOfPositive();
			log.info("before getDeviceImportData DeviceSerialNumber : {} ; todayImportEnergy : {} ; totalImportEnergy ： {} ", appVO.getDeviceSerialNumber(),
				todayImportEnergy, accumulatedEnergyOfPositive);
			resultAppReportHeaderVO.setTodayImportEnergy(this.getChangeEnergyResult(todayImportEnergy));
			resultAppReportHeaderVO.setTotalImportEnergy(this.getChangeEnergyResult(accumulatedEnergyOfPositive));
			SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.PATTERN_DATE);
			Date deviceDateTime = dbDeviceCurrentStatus.getDeviceDateTime();
			Date now = new Date();
			// 判断是否为今天,不为今天，则天数据为0
			if (!formatter.format(now).equalsIgnoreCase(formatter.format(deviceDateTime))) {
				resultAppReportHeaderVO.setTodayImportEnergy(BizConstant.BIGDECIMAL_DEFAULT_VALUE_ONE + BizConstant.UNIT_WH);
			}

		}
		LambdaQueryWrapper<Device23Entity> queryWrapper = Wrappers.<Device23Entity>query().lambda()
			.eq(Device23Entity::getPlantId, appVO.getPlantId());
		Device23Entity device23 = device23Service.getOne(queryWrapper);
		resultAppReportHeaderVO.setHybridWorkMode("");
		if (ObjUtil.isNotNull(device23) && ValidationUtil.isNotEmpty(device23.getHybridWorkMode())) {
			List<DictBiz> inverterMode = dictBizClient.getListByLang("inverter_mode", CommonUtil.getCurrentLanguage()).getData();
			DictBiz snj = inverterMode.stream()
				.filter(dict -> Func.equals(dict.getDictKey(), device23.getHybridWorkMode()))
				.filter(dict -> Func.equals(dict.getLanguage(), CommonUtil.getCurrentLanguage()))
				.filter(dict -> Func.equals(dict.getAttribute2(), "snj"))
				.findFirst()
				.orElse(null);

			if (ObjUtil.isNotNull(snj)) {
				resultAppReportHeaderVO.setHybridWorkMode(snj.getDictValue());
			}
		}
	}

	@NotNull
	private AppReportDetailVO getAppReportDetailVO(AppVO appVO, LocalDate beginLocalDate, LocalDate endLocalDate, SimpleDateFormat simpleDateFormat,
												   Function<QueryCondition, List<BatteryEverydayTotalVO>> fun
	) {
		QueryCondition queryCondition = new QueryCondition();
		queryCondition.setStartDateTime(Date.from(beginLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
		queryCondition.setEndDateTime(Date.from(endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
		queryCondition.setPlantId(appVO.getPlantId());
		queryCondition.setDeviceSerialNumber(appVO.getDeviceSerialNumber());
		List<BatteryEverydayTotalVO> batteryEverydayTotalList = fun.apply(queryCondition);
		AppReportDetailVO dailyReport = new AppReportDetailVO();
		List<AppReportDataVO> powerGeneration = new ArrayList<>();
		List<AppReportDataVO> dischargeCapacity = new ArrayList<>();
		for (BatteryEverydayTotalVO entity : batteryEverydayTotalList) {
			// 充电量
			AppReportDataVO dailyPower = new AppReportDataVO();
			// 放电量
			AppReportDataVO dailyDischarge = new AppReportDataVO();
			if (1 == appVO.getType()) {
				dailyDischarge.setKey(entity.getAppTotalDate().substring(5));
				dailyPower.setKey(entity.getAppTotalDate().substring(5));
			} else if (2 == appVO.getType() || 3 == appVO.getType()) {
				dailyDischarge.setKey(entity.getAppTotalDate());
				dailyPower.setKey(entity.getAppTotalDate());
			} else {
				dailyDischarge.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
				dailyPower.setKey(simpleDateFormat.format(entity.getAppTotalDate()));
			}
			BigDecimal appBatteryAccumulatedChargeEnergyTemp = entity.getAppBatteryAccumulatedChargeEnergy() == null ? new BigDecimal(0) : entity.getAppBatteryAccumulatedChargeEnergy();
			BigDecimal appBatteryDailyChargeEnergyTemp = entity.getAppBatteryDailyChargeEnergy() == null ? new BigDecimal(0) : entity.getAppBatteryDailyChargeEnergy();
			dailyDischarge.setValue(appBatteryAccumulatedChargeEnergyTemp.divide(new BigDecimal(1000), 4, RoundingMode.HALF_UP));
			dailyPower.setValue(appBatteryDailyChargeEnergyTemp.divide(new BigDecimal(1000), 4, RoundingMode.HALF_UP));
			dischargeCapacity.add(dailyDischarge);
			powerGeneration.add(dailyPower);
		}
		dailyReport.setPowerGeneration(powerGeneration);
		dailyReport.setDischargeCapacity(dischargeCapacity);
		return dailyReport;
	}

	@Override
	public AppDeviceDetail queryDeviceDetail(AppVO appVO) {
		AppDeviceDetail appDeviceDetail = new AppDeviceDetail();
		List<AppDeviceInfo> appDeviceInfoList = device21Service.queryAppInverterInfo(appVO.getPlantId(), appVO.getDeviceSerialNumber());
		if (CollectionUtil.isNotEmpty(appDeviceInfoList)) {
			AppDeviceInfo appDeviceInfo = appDeviceInfoList.get(0);
			appDeviceInfo.setNewQualityGuaranteeYearDate(appDeviceInfo.getWarrantyDeadline());
			appDeviceDetail.setInverterInfo(appDeviceInfo);
		}
		List<AppBatteryCurrentStatusInfo> appBatteryCurrentStatusInfoList = batteryCurrentStatusService.queryAppBatteryInfo(appVO.getPlantId(), appVO.getDeviceSerialNumber());
		if (CollectionUtils.isEmpty(appBatteryCurrentStatusInfoList)) {
			return appDeviceDetail;
		}
		AppBatteryCurrentStatusInfo appBatteryCurrentStatusInfo = appBatteryCurrentStatusInfoList.get(0);
		appDeviceDetail.setBatteryInfo(appBatteryCurrentStatusInfo);
		return appDeviceDetail;
	}

	@Override
	public AppDeviceDetail queryDeviceDetailV2(AppVO appVO) {
		AppDeviceDetail appDeviceDetail = new AppDeviceDetail();
		// 兼容只扫描了逆变器，没有配网的情形，避免下面关联查询时，没有数据
		this.getDeviceExitFactoryInfo(appVO, appDeviceDetail);
		// 查询设备信息
		BladeUser user = AuthUtil.getUser();
		// 用户类型
		String userType = StatusDisplayUtil.getRoleType(user.getRoleName(), user.getDeptId());
		List<AppDeviceInfo> appDeviceInfoList = device21Service.queryAppInverterInfoV2(appVO.getPlantId(), appVO.getDeviceSerialNumber());
		if (CollectionUtil.isNotEmpty(appDeviceInfoList)) {
			AppDeviceInfo appDeviceInfo = appDeviceInfoList.get(0);
			appDeviceInfo.setNewQualityGuaranteeYearDate(appDeviceInfo.getWarrantyDeadline());
			//  在线状态转换
			appDeviceInfo.setOnLineStatus(StatusDisplayUtil.plantAndInverterStatusConvert(appDeviceInfo.getOnLineStatus(), userType,
				appDeviceInfo.getExistUserTypeAlarm(), appDeviceInfo.getExistAgentTypeAlarm()));
			List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntityList = deviceExitFactoryInfoService.getListByDeviceSerialNumberCollect(Lists.newArrayList(appDeviceInfo.getDeviceSerialNumber()));
			Map<String, String> deviceExitCollect = deviceExitFactoryInfoEntityList.stream().collect(Collectors.toMap(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getDeviceType));
			String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, deviceExitCollect.get(appVO.getDeviceSerialNumber()));
			appDeviceInfo.setInverterKind(value);
			appDeviceDetail.setInverterInfo(appDeviceInfo);
		}
		// 查询电池信息
		List<AppBatteryCurrentStatusInfo> appBatteryCurrentStatusInfoList = batteryCurrentStatusService.queryAppBatteryInfoV2(appVO.getPlantId(), appVO.getDeviceSerialNumber());
		if (CollectionUtils.isEmpty(appBatteryCurrentStatusInfoList)) {
			log.info("queryDeviceDetailV2 appBatteryCurrentStatusInfoList is null");
			return appDeviceDetail;
		}
		AppBatteryCurrentStatusInfo appBatteryCurrentStatusInfo = appBatteryCurrentStatusInfoList.get(0);
		appDeviceDetail.setBatteryInfo(appBatteryCurrentStatusInfo);
		if (appBatteryCurrentStatusInfo.getNumberOfBattery() != null) {
//			BigDecimal bigDecimal = CommonConstant.SINGLE_BATTERY_RATED_CAPACITY.multiply(appBatteryCurrentStatusInfo.getNumberOfBattery()).setScale(2, RoundingMode.HALF_UP);
			AppDeviceInfo inverterInfo = appDeviceDetail.getInverterInfo();
			if (Objects.isNull(inverterInfo)) {
				log.info("queryDeviceDetailV2 inverterInfo is null");
				return appDeviceDetail;
			}
			String deviceModel = inverterInfo.getDeviceModel();
			if (StringUtil.isBlank(deviceModel)) {
				log.info("queryDeviceDetailV2 deviceModel is null");
				return appDeviceDetail;
			}
			R<List<DictBiz>> dictBizList = dictBizClient.getListAllLang(DictBizCodeEnum.CLIENT_DEVICE_MODE_TYPE.getDictCode());
			if (Objects.isNull(dictBizList) || CollectionUtils.isEmpty(dictBizList.getData())) {
				log.info("queryDeviceDetailV2 dictBizList is null");
				return appDeviceDetail;
			}
			List<DictBiz> capacityList = dictBizList.getData().stream().filter(dictBiz -> deviceModel.equals(dictBiz.getDictKey())).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(capacityList)) {
				log.info("queryDeviceDetailV2 capacityList is null");
				return appDeviceDetail;
			}
			DictBiz dictBiz = capacityList.get(0);
			String capacity = dictBiz.getAttribute1();
			if (StringUtil.isBlank(capacity)) {
				log.info("queryDeviceDetailV2 capacity is null");
				return appDeviceDetail;
			}
			BigDecimal bigDecimal = new BigDecimal(capacity).multiply(appBatteryCurrentStatusInfo.getNumberOfBattery()).setScale(2, RoundingMode.HALF_UP);
			appBatteryCurrentStatusInfo.setTotalRatedCapacity(bigDecimal);
		}

		return appDeviceDetail;
	}

	@NotNull
	private AppDeviceInfo getDeviceExitFactoryInfo(AppVO appVO, AppDeviceDetail appDeviceDetail) {
		PlantEntity plantEntity = plantService.getById(appVO.getPlantId());
		AppDeviceInfo deviceInfo = new AppDeviceInfo();
		deviceInfo.setPlantName(plantEntity.getPlantName());
		deviceInfo.setInstallDate(plantEntity.getInstallDate());
		LambdaQueryWrapper<DeviceExitFactoryInfoEntity> lambdaQueryWrapper = Wrappers.<DeviceExitFactoryInfoEntity>query().lambda()
			.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, appVO.getDeviceSerialNumber());
		List<DeviceExitFactoryInfoEntity> factoryList = deviceExitFactoryInfoService.list(lambdaQueryWrapper);
		if (CollectionUtil.isNotEmpty(factoryList)) {
			DeviceExitFactoryInfoEntity factoryInfoEntity = factoryList.get(0);
			deviceInfo.setDeviceSerialNumber(factoryInfoEntity.getDeviceSerialNumber());
			deviceInfo.setDeviceModel(factoryInfoEntity.getDeviceType());
			deviceInfo.setQualityGuaranteeYear(factoryInfoEntity.getQualityGuaranteeYear());
			deviceInfo.setWarrantyStartDate(factoryInfoEntity.getWarrantyStartDate());
			if (StringUtil.isNotBlank(factoryInfoEntity.getNewQualityGuaranteeYear())) {
				deviceInfo.setNewQualityQuaranteeYear(factoryInfoEntity.getQualityGuaranteeYear());
			}
			if (StringUtil.isNotBlank(factoryInfoEntity.getNewQualityGuaranteeYear())) {
				String warrantyStartDate = factoryInfoEntity.getWarrantyStartDate();
				String qualityGuaranteeYear = factoryInfoEntity.getQualityGuaranteeYear();
				if (ValidationUtil.isNotEmpty(warrantyStartDate) && ValidationUtil.isNotEmpty(qualityGuaranteeYear)) {
					String warrantyDeadline = BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear), warrantyStartDate);
					deviceInfo.setNewQualityGuaranteeYearDate(warrantyDeadline);
				}
			}
		}
		appDeviceDetail.setInverterInfo(deviceInfo);
		return deviceInfo;
	}

	@Override
	public List<AppBatteryExitFactoryInfoVO> queryBatteryExitFactoryList(AppVO appVO) {
		List<AppBatteryExitFactoryInfoVO> appBatteryExitFactoryInfoVOS = batteryExitFactoryInfoService.queryAppBatteryExitFactoryInfo(appVO.getPlantId(), appVO.getDeviceSerialNumber());
		for (AppBatteryExitFactoryInfoVO vo : appBatteryExitFactoryInfoVOS) {
			String warrantyStartDate = vo.getWarrantyStartDate();
			String qualityGuaranteeYear = vo.getQualityGuaranteeYear();
			if (StringUtils.isNotEmpty(warrantyStartDate) && StringUtils.isNotEmpty(qualityGuaranteeYear)) {
				String warrantyDeadline = BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear), warrantyStartDate);
				vo.setQualityQuaranteeDate(warrantyDeadline);
			}
		}
		return appBatteryExitFactoryInfoVOS;
	}

	@Override
	public boolean deleteBattery(Long batteryMapDeviceId) {
		if (batteryMapDeviceId == null) {
			return false;
		}
		BladeUser user = AuthUtil.getUser();
		BatteryMapDeviceEntity queryEntity = new BatteryMapDeviceEntity();
		queryEntity.setId(batteryMapDeviceId);
		// 在关联查询出厂设置表
		List<BatteryMapDeviceVO> batteryMapDeviceVOList = batteryMapDeviceService.queryBatteryDeviceInfo(queryEntity);
		if (CollectionUtil.isNotEmpty(batteryMapDeviceVOList)) {
			BatteryMapDeviceVO batteryMapDeviceVO = batteryMapDeviceVOList.get(CommonConstant.NOT_SEALED_ID);
			// 删除电池后更新电池出厂设置为 未使用
			LambdaUpdateWrapper<BatteryExitFactoryInfoEntity> update = Wrappers.<BatteryExitFactoryInfoEntity>update().lambda()
				.set(BatteryExitFactoryInfoEntity::getStatus, 0)
				.set(BatteryExitFactoryInfoEntity::getUpdateUserAccount, user.getAccount())
				.set(BatteryExitFactoryInfoEntity::getUpdateUser, user.getUserId())
				.eq(BatteryExitFactoryInfoEntity::getBatterySerialNumber, batteryMapDeviceVO.getBatterySerialNumber());
			batteryExitFactoryInfoService.update(update);
			PlantEntity updatePlantEntity = new PlantEntity();
			updatePlantEntity.setId(batteryMapDeviceVO.getPlantId());
			updatePlantEntity.setBatteryNumber(-1);
			plantService.updatePlant(updatePlantEntity);
			// 删除电池更改逆变器质保期限


			// 删除电池的重要事件
			this.saveBatteryImportantEventByDelete(batteryMapDeviceVO, user);
		}

		return batteryMapDeviceService.deleteLogic(Lists.newArrayList(batteryMapDeviceId));
	}


	@NotNull
	private DeviceCustomModeEntity getDeviceCustomModeEntity(List<Device23Entity> dbResultList) {
		DeviceCustomModeEntity entity = new DeviceCustomModeEntity();
		Device23Entity dbResult = dbResultList.get(0);
		String hybridWorkMode = dbResult.getHybridWorkMode();
		entity.setOnceEveryday(dbResult.getOnceEveryday());
		entity.setChargeStartTime1(dbResult.getChargeStartTime1());
		entity.setChargeEndTime1(dbResult.getChargeEndTime1());
		entity.setDischargeStartTime1(dbResult.getDischargeStartTime1());
		entity.setDischargeEndTime1(dbResult.getDischargeEndTime1());
		entity.setCapacityOfChargerEnd(dbResult.getCapacityOfChargerEnd() == null ? 0 : dbResult.getCapacityOfChargerEnd().intValue());
		entity.setCapacityOfDischargerEnd(dbResult.getCapacityOfDischargerEnd() == null ? 0 : dbResult.getCapacityOfDischargerEnd().intValue());
		entity.setMaximumChargerPower(dbResult.getMaximumChargerPower() == null ? 0 : dbResult.getMaximumChargerPower().intValue());
		entity.setMaximumDischargerPower(dbResult.getMaximumDischargerPower() == null ? 0 : dbResult.getMaximumDischargerPower().intValue());
		entity.setHybridWorkMode(hybridWorkMode);
		entity.setChargePowerInTime1HighWord(dbResult.getChargePowerInTime1HighWord() == null ? "0" : dbResult.getChargePowerInTime1HighWord());
		entity.setChargeEndSocInTime1(dbResult.getChargeEndSocInTime1() == null ? "0" : dbResult.getChargeEndSocInTime1());
		entity.setDischargePowerInTime1HighWord(dbResult.getDischargePowerInTime1HighWord() == null ? "0" : dbResult.getDischargePowerInTime1HighWord());
		entity.setDischargeEndSocInTime1(dbResult.getDischargeEndSocInTime1() == null ? "0" : dbResult.getDischargeEndSocInTime1());
		return entity;
	}


	@Transactional(rollbackFor = Exception.class)
	@Override
	public R<Boolean> deletePlant(String ids) {
		R r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (StringUtil.isBlank(ids)) {
			return R.fail("the plant id cannot be empty.");
		}
		List<Long> longList = Func.toLongList(ids);
		List<PlantEntity> plantEntities = plantService.listByIds(longList);
		if (CollectionUtils.isEmpty(plantEntities)) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.autoGetMessage(currentLanguage));
			r.setSuccess(false);
			return r;

		} else {
			for (PlantEntity plantEntity : plantEntities) {
				// 如果该id查不到数据，还调用该接口，则提示异常
				if (!longList.contains(plantEntity.getId())) {
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100028.autoGetMessage(currentLanguage));
					r.setSuccess(false);
					return r;
				}
			}
		}

		BladeUser user = AuthUtil.getUser();

		List<ImportantEventEntity> insertList = new ArrayList<>();
		for (Long plantId : longList) {
			ImportantEventEntity importantEventEntity = new ImportantEventEntity();
			importantEventEntity.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_PLANT);
			importantEventEntity.setPlantId(plantId);
			importantEventEntity.setEventContent(DeviceEventContentMultiLanguageEnum.PLANT_EVENT_DELETE.getDeviceEventByLang(currentLanguage));
			importantEventEntity.setEventDate(new Date());
			importantEventEntity.setCreateUserAccount(user.getAccount());
			importantEventEntity.setCreateUser(user.getUserId());
			insertList.add(importantEventEntity);
		}
		// delete battery important
		importantEventService.saveBatch(insertList);

		// 将电站下 逆变器出厂设备  更新为 未使用
		List<WifiStickPlantEntity> wifiStickPlantEntities = wifiStickPlantService.queryDeviceSerialNumberList(longList);
		List<String> deviceSerialNumberList = new ArrayList<>();
		for (WifiStickPlantEntity entity : wifiStickPlantEntities) {
			Long plantId = longList.get(BizConstant.NUMBER_ZERO);
			String deviceSerialNumber = entity.getDeviceSerialNumber();
			deviceSerialNumberList.add(deviceSerialNumber);
			//发送mqtt通知设备,逆变器与站点已经解绑
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("deviceSn", deviceSerialNumber);
			jsonObject.put("topic", Constants.UNBIND_INVERTER);
			deviceIssueBiz.dataIssueToDevice(jsonObject);
			// 删除设备21表数据
			device21Service.deleteByPlantIdAndSn(plantId, deviceSerialNumber);
			device23Service.deleteByPlantIdAndSn(plantId, deviceSerialNumber);
			device24Service.deleteByPlantIdAndSn(plantId, deviceSerialNumber);
			// 删除battery_current_status、device_current_status相关数据
			batteryCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId,deviceSerialNumber, user.getAccount());
			deviceCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId,deviceSerialNumber, user.getAccount());
		}

		// 将电站下的 出厂设备信息 中 电池更新为未使用
		List<BatteryMapDeviceEntity> batteryMapDeviceEntities = batteryMapDeviceService.queryListByPlantId(longList);
		if (batteryMapDeviceEntities != null && !batteryMapDeviceEntities.isEmpty()) {
			List<String> batterySerialList = batteryMapDeviceEntities.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
			batteryExitFactoryInfoService.batchUpdate(batterySerialList, BizConstant.NUMBER_ZERO);
		}
		if (!deviceSerialNumberList.isEmpty()) {
			deviceExitFactoryInfoService.batchUpdate(deviceSerialNumberList);
		}
		// 删除 站点 和 逆变器 的关系
		wifiStickPlantService.batchDeleteLogicByPlantId(longList, user.getAccount());
		// 删除 站点 和 电池的 关系
		batteryMapDeviceService.batchDeleteLogicByPlantId(longList, user.getAccount());
		plantService.deleteLogic(longList);

		//删除设备时区表的数据
		timeZoneDeviceService.deleteByPlantId(longList);
		// 删除设备告警信息
		alarmLogService.update(Wrappers.<AlarmLogEntity>lambdaUpdate()
				.set(AlarmLogEntity::getIsDeleted, BizConstant.CHAR_ONE)
			.in(AlarmLogEntity::getPlantId, longList).eq(AlarmLogEntity::getIsDeleted, BizConstant.CHAR_ZERO));
		return R.success("successfully delete.");
	}

	@Override
	public OtaUpdatePackEntity upgrading(Map<String, Object> map) {
		Object currentVersion = map.get("currentVersion");
		Object bigType = map.get("bigType");
		Object smallType = map.get("smallType");
		if (Objects.isNull(currentVersion) || Objects.isNull(bigType) || Objects.isNull(smallType)) {
			throw new BusinessException("client.parameter.error.empty");
		}
		OtaUpdatePackEntity otaUpdatePackEntity = otaUpdatePackService.getOne(Wrappers.<OtaUpdatePackEntity>query().lambda().eq(OtaUpdatePackEntity::getSmallType, smallType).eq(OtaUpdatePackEntity::getBigType, bigType).eq(OtaUpdatePackEntity::getCompany, CommonConstant.SKY_WORTH).eq(OtaUpdatePackEntity::getIsDeleted, BizConstant.CHAR_ZERO).eq(OtaUpdatePackEntity::getIsNewVersion, BizConstant.CHAR_ONE).ne(OtaUpdatePackEntity::getVersionNumber, currentVersion)
		);
		if (otaUpdatePackEntity != null) {
			String latestVersion = otaUpdatePackEntity.getVersionNumber();
			if (!latestVersion.equals(currentVersion)) {
				Long businessId = otaUpdatePackEntity.getBusinessId();
				List<AttachmentInfoEntity> attachmentInfoEntityList = attachmentInfoService.findByBusinessIdsNoTent(Collections.singletonList(businessId)).getData().get(businessId);
				if (!attachmentInfoEntityList.isEmpty()) {
					otaUpdatePackEntity.setPackCdnUrl(otaUpdatePackEntity.getPackCdnUrl() + CommonConstant.SYMBOL_QUESTION_MARK + attachmentInfoEntityList.get(0).getSasToken());
				}
			} else {
				return null;
			}
		}
		return otaUpdatePackEntity;
	}

	private DeviceCustomModeEntity queryInverterMode(Long plantId,String deviceSerialNumber) {
		LambdaQueryWrapper<Device23Entity> lambdaQueryWrapper = Wrappers.<Device23Entity>query().lambda()
			.eq(Device23Entity::getPlantId, plantId);
		// 兼容旧app
		if (ValidationUtil.isNotEmpty(deviceSerialNumber)){
			lambdaQueryWrapper = Wrappers.<Device23Entity>query().lambda()
				.eq(Device23Entity::getPlantId, plantId)
				.eq(Device23Entity::getDeviceSerialNumber, deviceSerialNumber);
		}
		List<Device23Entity> dbResultList = device23Service.list(lambdaQueryWrapper);
		if (CollectionUtils.isEmpty(dbResultList)) {
			return new DeviceCustomModeEntity();
		}
		return this.getDeviceCustomModeEntity(dbResultList);
	}

	@Override
	public List<InverterModeVO> inverterList(AppVO appVO) {
		List<WifiStickPlantVO> wifiStickPlantVoS = wifiStickPlantService.queryDeviceCompany(appVO);
		String companyCode = Optional.ofNullable(wifiStickPlantVoS).orElse(new ArrayList<>()).stream().
			map(WifiStickPlantVO::getCompany).findFirst().orElse("");
		R<List<DictBiz>> inverterMode = dictBizClient.getList("inverter_mode");
		List<DictBiz> dictData = inverterMode.getData();
		List<DictBiz> dictBizList = dictData.stream().filter(p -> companyCode.equals(p.getAttribute2()) && p.getLanguage().equals(appVO.getLanguage())).collect(Collectors.toList());
		DeviceCustomModeEntity currentMode = queryInverterMode(appVO.getPlantId(),ValidationUtil.isNotEmpty(appVO.getDeviceSerialNumber()) ? appVO.getDeviceSerialNumber() : "");
		List<InverterModeVO> result = dictBizList.stream().map(p -> {
			InverterModeVO vo = new InverterModeVO();
			BeanUtil.copy(p, vo);
			if (p.getDictKey().equalsIgnoreCase(currentMode.getHybridWorkMode())) {
				vo.setCurrentDeivceMode("1");
			}
			return vo;
		}).collect(Collectors.toList());
		log.info("get inverterList : {}", result);
		return result;
	}

	@Override
	public R<String> editPlant(PlantEntity plantEntity) {
		R<String> r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (StringUtil.isBlank(plantEntity.getPlantName()) || StringUtil.isBlank(plantEntity.getCountryCode())
			|| StringUtil.isBlank(plantEntity.getDetailAddress())) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100008.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100008.autoGetMessage(currentLanguage));
			return r;
		}
		if (plantEntity.getId() == null) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100026.getCode());
			r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100026.autoGetMessage(currentLanguage));
			return r;
		}


		Wrapper<PlantEntity> wrapper = Wrappers.<PlantEntity>lambdaQuery().eq(PlantEntity::getPlantName, plantEntity.getPlantName()).eq(
			PlantEntity::getCreateUser, AuthUtil.getUser().getUserId()).ne(
			PlantEntity::getId, plantEntity.getId());
		long count = plantService.count(wrapper);
		if (count != 0) {
			r.setCode(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100001.getCode());
			r.setMsg(StringUtil.format(I18nMsgCode.SKYWORTH_CLIENT_PLANT_100001.getMessage(), plantEntity.getPlantName()));
			return r;
		}
		// 根据id查询原有信息
		PlantEntity plantOld = plantService.getById(plantEntity.getId());
		plantOld.setPlantName(plantEntity.getPlantName());
		plantOld.setCountryCode(plantEntity.getCountryCode());
		plantOld.setProvinceCode(plantEntity.getProvinceCode());
		plantOld.setCityCode(plantEntity.getCityCode());
		plantOld.setCountyCode(plantEntity.getCountyCode());
		plantOld.setDetailAddress(plantEntity.getDetailAddress());
		plantOld.setTimeZone(plantEntity.getTimeZone());
		plantOld.setOperationUserId(plantEntity.getOperationUserId());
		plantOld.setOperationCompanyId(plantEntity.getOperationCompanyId());
		plantService.updateById(plantOld);
		return R.data("操作成功");
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public R<Boolean> deleteUser(AppVO appVO) {
		R<Boolean> r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		String verificationType = appVO.getVerificationType();
		String smsTemplate = "";
		String phone = "";
		String emailAddress = "";
		// 邮箱方式删除账号
		if (ValidationUtil.isNotEmpty(verificationType) && "0".equals(verificationType)) {
			// 获取验证码
			emailAddress = appVO.getEmail();
			String code = appVO.getVerificationCode();
			String redisCode = bladeRedis.get(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_DELETE_CAPTCHA_SUFFIX.getCacheNameSuffix() + emailAddress);
			// 判断验证码
			if (StringUtil.isBlank(redisCode) || StringUtil.isBlank(code)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
			if (!StringUtil.equalsIgnoreCase(code, redisCode)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
			// verificationType为空或者1就是手机号删除账户
		} else if (ValidationUtil.isEmpty(verificationType) || (ValidationUtil.isNotEmpty(verificationType) && "1".equals(verificationType))) {
			String phoneDiallingCode = appVO.getPhoneDiallingCode();
			if (StringUtil.isBlank(phoneDiallingCode)) {
				r.setCode(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_RESOURCE_SMS_100103.autoGetMessage(currentLanguage));
				return r;
			}
			String trim = phoneDiallingCode.trim();

			if (!trim.contains("+")) {
				trim = "+" + trim;
			}
			phone = trim + appVO.getPhone();
			String code = appVO.getVerificationCode();
			// 获取验证码
//        R r1 = smsClient.validateMessage(SmsConstant.SMS_CODE_4_REGISTERING_OR_DELETION, appVO.getSmsId(), appVO.getVerificationCode(), phone);
			String getTemplateLanguage = CommonUtil.getCurrentLanguage();
			if (phoneDiallingCode.contains("86")) {
				getTemplateLanguage = CommonConstant.CURRENT_LANGUAGE_ZH;
			}
			smsTemplate = SmsEnum.getSmsTemplate(SmsEnum.FIVE.getSmsType(), getTemplateLanguage);
			String smsCode = bladeRedis.get(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
			if (StringUtil.isBlank(smsCode) || StringUtil.isBlank(code)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
			if (!StringUtil.equalsIgnoreCase(code, smsCode)) {
				r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.getCode());
				r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_USER_100006.autoGetMessage(currentLanguage));
				return r;
			}
		}

		BladeUser user = AuthUtil.getUser();
		List<WifiStickPlantEntity> wifiStickPlantEntities = wifiStickPlantService.queryOwnerData(user.getUserId());
		List<BatteryMapDeviceEntity> batteryMapDeviceEntities = batteryMapDeviceService.queryOwnerData(user.getUserId());

		PlantEntity updatePlant = new PlantEntity();
		updatePlant.setIsDeleted(1);
		updatePlant.setCreateUser(user.getUserId());
		updatePlant.setUpdateUser(user.getUserId());
		updatePlant.setUpdateUserAccount(user.getAccount());

		WifiStickPlantEntity updateWifi = new WifiStickPlantEntity();
		updateWifi.setIsDeleted(1);
		updateWifi.setCreateUser(user.getUserId());
		updateWifi.setUpdateUser(user.getUserId());
		updateWifi.setUpdateUserAccount(user.getAccount());

		BatteryMapDeviceEntity updateBatteryMap = new BatteryMapDeviceEntity();
		updateBatteryMap.setIsDeleted(1);
		updateBatteryMap.setCreateUser(user.getUserId());
		updateBatteryMap.setUpdateUser(user.getUserId());
		updateBatteryMap.setUpdateUserAccount(user.getAccount());

		// 将 逆变器 、电池 出厂设置 状态改为 未使用
		if (wifiStickPlantEntities != null && !wifiStickPlantEntities.isEmpty()) {
			List<String> deviceSerialList = wifiStickPlantEntities.stream().map(WifiStickPlantEntity::getDeviceSerialNumber).collect(Collectors.toList());
			deviceExitFactoryInfoService.batchUpdate(deviceSerialList);
			// 删除设备21表数据
			for (WifiStickPlantEntity update : wifiStickPlantEntities) {
				Long plantId = update.getPlantId();
				String deviceSerialNumber = update.getDeviceSerialNumber();
				device21Service.deleteByPlantIdAndSn(plantId, deviceSerialNumber);
				device23Service.deleteByPlantIdAndSn(plantId, deviceSerialNumber);
				device24Service.deleteByPlantIdAndSn(plantId, deviceSerialNumber);
				//发送mqtt通知设备,逆变器与站点已经解绑
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("deviceSn", update.getDeviceSerialNumber());
				jsonObject.put("topic", Constants.UNBIND_INVERTER);
				deviceIssueBiz.dataIssueToDevice(jsonObject);
				// 删除battery_current_status、device_current_status相关数据
				batteryCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId,deviceSerialNumber, user.getAccount());
				deviceCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId,deviceSerialNumber, user.getAccount());
			}
		}
		if (batteryMapDeviceEntities != null && !batteryMapDeviceEntities.isEmpty()) {
			List<String> batterySerialList = batteryMapDeviceEntities.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
			batteryExitFactoryInfoService.batchUpdate(batterySerialList, BizConstant.NUMBER_ZERO);
		}
		userClient.deleteUserById(user.getUserId());
		// 删除当前用户下 逆变器 和 站点关系
		wifiStickPlantService.updateDataByCondition(updateWifi);
		// 删除电池 和 站点关系
		batteryMapDeviceService.updateDataByCondition(updateBatteryMap);
		// 删除当前用户下所有站点
		plantService.updatePlant(updatePlant);

		if (ValidationUtil.isNotEmpty(verificationType) && "0".equals(verificationType)) {
			bladeRedis.del(CacheNames.CAPTCHA_KEY + EmailCacheNamesEnum.EMAIL_DELETE_CAPTCHA_SUFFIX.getCacheNameSuffix() + emailAddress);
		} else if (ValidationUtil.isEmpty(verificationType) || "1".equals(verificationType)) {
			bladeRedis.del(SmsConstant.CAPTCHA_KEY + smsTemplate + StringPool.COLON + phone);
		}
		List<cn.hutool.json.JSONObject> agentList = new ArrayList<>();
		cn.hutool.json.JSONObject object = new cn.hutool.json.JSONObject();
		object.set("userId", user.getUserId());
		object.set("userType", "constructor");
		agentList.add(object);
		agentClient.cleanUpAgentUser(agentList);

		return R.data(true);
	}

	/**
	 * 根据植物ID查询设备的日期和时区信息
	 * 此方法首先从数据库中获取指定站点ID的时区设备信息，然后根据获取到的时区信息
	 * 将当前时间转换为该时区的日期格式，并将日期和时区信息封装到JSONObject中返回
	 * 如果未找到对应的时区设备信息或时区信息为空，则使用默认时区
	 *
	 * @param plantId 站点ID，用于查询对应的时区设备信息
	 * @return 包含设备日期和时区信息的JSONObject
	 */
	@Override
	public JSONObject queryDeviceDateAndTimeZone(Long plantId) {
		Map<String, String> timeZoneMap = timeZoneDeviceService.getMapFromCacheByPlantIdList(Collections.singletonList(plantId));
		// 查询指定站点ID的时区设备信息，且设备未被删除
		String timeZone = timeZoneMap.getOrDefault(plantId + "", CommonConstant.COMMON_DEFAULT_TIME_ZONE);
		// 获取当前时间，并将其从默认时区转换为设备所在时区
		long currentDateTimeLong = Instant.now().getEpochSecond();
		Date date = DateUtil.convertLongTimeZone(currentDateTimeLong, CommonConstant.COMMON_DEFAULT_TIME_ZONE,
			timeZone);
		// 将转换后的日期格式化为字符串
		SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.PATTERN_DATE);
		String dateStr = dateFormat.format(date);
		// 创建JSONObject用于存储日期和时区信息
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("date", dateStr);
		jsonObject.put("timeZone", timeZone);
		// 返回包含日期和时区信息的JSONObject
		return jsonObject;
	}

	@Override
	public R<String> unbindWifiDongle(WifiStickPlantEntity wifiStickPlantEntity) {
		Long plantId = wifiStickPlantEntity.getPlantId();
		String unBindDeviceSerialNumber = wifiStickPlantEntity.getDeviceSerialNumber();
		// 根据站点id查询已经绑定的wifiStick列表，如果没有绑定，则返回错误
		// 解绑使用逻辑删除，恢复出厂信息标识
		WifiStickPlantEntity queryEntity = new WifiStickPlantEntity();
		queryEntity.setPlantId(wifiStickPlantEntity.getPlantId());
		List<WifiStickPlantEntity> list = wifiStickPlantService.list(Condition.getQueryWrapper(queryEntity));
		// 如果list中没有要解绑的SN，则直接返回错误，不能解绑
		Optional<WifiStickPlantEntity> target =
			list.stream().filter(entity -> unBindDeviceSerialNumber.equals(entity.getDeviceSerialNumber())).findFirst();
		if(target.isEmpty()){
			throw new BusinessException("client.inverter.unable.unbind.limit");
		}
		WifiStickPlantEntity updateWifi = target.get();
		//解绑操作
		unbindWifiDongle(updateWifi.getPlantId(), updateWifi.getDeviceSerialNumber());
		PlantEntity plantEntity = new PlantEntity();
		LambdaQueryWrapper<Device23Entity> queryOtherSn = Wrappers.<Device23Entity>query().lambda().eq(Device23Entity::getPlantId, plantId)
			.ne(Device23Entity::getDeviceSerialNumber, unBindDeviceSerialNumber).eq(Device23Entity::getParallelModeFunction,Constants.ONE);
		long dbOtherSnCount = device23Service.count(queryOtherSn);
		// 如果解绑站点下还有其他逆变器时并机，则站点为并机，如果站点下其他逆变器全部不是并机，则站点为非并机
		if (dbOtherSnCount == 0) {
			plantEntity.setIsParallelMode(BizConstant.CLIENT_PLANT_PARALLEL_MODE_NO);
		} else {
			plantEntity.setIsParallelMode(BizConstant.CLIENT_PLANT_PARALLEL_MODE_YES);
		}
		long batteryCountBySn = batteryMapDeviceService.count(Wrappers.<BatteryMapDeviceEntity>lambdaQuery()
			.eq(BatteryMapDeviceEntity::getPlantId, plantId)
			.ne(BatteryMapDeviceEntity::getDeviceSerialNumber, unBindDeviceSerialNumber));
		plantEntity.setId(plantId);
		plantEntity.setDeviceNumber(-1);
		plantEntity.setBatteryNumber(-Math.toIntExact(batteryCountBySn));
		queryPlantStatus(plantId,plantEntity,unBindDeviceSerialNumber);
		plantService.updatePlant(plantEntity);
		return R.success("success");
	}

	/**
	 * 根据站点ID查询站点状态，并更新到plantEntity中。
	 * 状态包括：离线（CHAR_ZERO）、在线（CHAR_ONE）、告警（CHAR_TWO）。
	 * 同时设置是否存在用户告警和代理告警标志。
	 *
	 * @param plantId           站点ID
	 * @param plantEntity       需要更新状态的站点实体
	 * @param deviceSerialNumber 当前操作的设备序列号（用于排除自己）
	 */
	private void queryPlantStatus(Long plantId, PlantEntity plantEntity, String deviceSerialNumber) {
		// 定义常用常量，提高可读性和避免重复调用
		final String charZero = BizConstant.CHAR_ZERO;
		final String charOne = BizConstant.CHAR_ONE;
		final String charTwo = BizConstant.CHAR_TWO;
		final Integer numberZero = BizConstant.NUMBER_ZERO;
		final Integer numberOne = BizConstant.NUMBER_ONE;
		// 查询除当前设备外的所有设备的状态（非删除状态）
		List<Device21Entity> device21EntityList = device21Service.list(
			Wrappers.lambdaQuery(Device21Entity.class)
				.select(Device21Entity::getId, Device21Entity::getDeviceStatus)
				.eq(Device21Entity::getPlantId, plantId)
				.ne(Device21Entity::getDeviceSerialNumber, deviceSerialNumber)
				.eq(Device21Entity::getIsDeleted, numberZero));
		// 如果没有其他设备，则站点状态设为离线，并清空告警标志
		if (device21EntityList.isEmpty()) {
			plantEntity.setPlantStatus(charZero);
			plantEntity.setExistAgentTypeAlarm(numberZero);
			plantEntity.setExistUserTypeAlarm(numberZero);
			return;
		}
		// 查询该站点下所有未解决的告警（异常类型为逆变器+电池相关的）
		List<AlarmLogEntity> allUnresolvedAlarms = alarmLogService.list(Wrappers.<AlarmLogEntity>lambdaQuery()
			.eq(AlarmLogEntity::getPlantId, plantId)
			.eq(AlarmLogEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED)
			.in(AlarmLogEntity::getExceptionType,
				Arrays.asList(AlarmTypeEnum.DEVICE.getType(), AlarmTypeEnum.BATTERY.getType()))
			.ne(AlarmLogEntity::getStatus, BizConstant.NUMBER_ONE));
		// 初始化用户告警和代理告警标志为0（无告警）
		int existUserTypeAlarm = numberZero;
		int existAgentTypeAlarm = numberZero;
		// 遍历告警列表，判断是否存在用户或代理告警
		for (AlarmLogEntity alarm : allUnresolvedAlarms) {
			if (alarm.getUserId() != null) {
				existUserTypeAlarm = numberOne; // 存在用户告警
			}
			if (alarm.getDepartmentId() != null) {
				existAgentTypeAlarm = numberOne; // 存在代理告警
			}
			// 如果两种告警都存在，提前退出循环
			if (existUserTypeAlarm == numberOne && existAgentTypeAlarm == numberOne) {
				break;
			}
		}
		// 设置用户和代理告警标志
		plantEntity.setExistUserTypeAlarm(existUserTypeAlarm);
		plantEntity.setExistAgentTypeAlarm(existAgentTypeAlarm);
		// 统计离线设备数量
		long offLineCount = device21EntityList.stream()
			.filter(device -> charZero.equals(device.getDeviceStatus()))
			.count();
		// 判断站点状态
		if (offLineCount == device21EntityList.size()) {
			// 所有设备都离线，站点状态为离线
			plantEntity.setPlantStatus(charZero);
		} else if (!allUnresolvedAlarms.isEmpty()) {
			// 存在任何未解决的告警，站点状态为告警
			plantEntity.setPlantStatus(charTwo);
		} else {
			// 无告警且不是全部离线，站点状态为在线
			plantEntity.setPlantStatus(charOne);
		}
	}

	@Override
	public Boolean inverterParallelEnable(AppVO appVO) {
		Long plantId = appVO.getPlantId();

		WifiStickPlantEntity wifiStickPlantQuery = new WifiStickPlantEntity();
		wifiStickPlantQuery.setPlantId(plantId);

		List<WifiStickPlantEntity> list = wifiStickPlantService.list(Condition.getQueryWrapper(wifiStickPlantQuery));
		if (CollectionUtil.isNotEmpty(list)){
			if (list.size() == BizConstant.CLIENT_INVERTER_MAX_PARALLEL){
				return false;
			}else if (list.size() >= 1){
				WifiStickPlantEntity wifiStickPlant = list.get(0);
				DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = new DeviceExitFactoryInfoEntity();
				deviceExitFactoryInfoEntity.setDeviceSerialNumber(wifiStickPlant.getDeviceSerialNumber());
				DeviceExitFactoryInfoEntity factoryInfoEntity = deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(deviceExitFactoryInfoEntity));
				if (ObjectUtil.isNotEmpty(factoryInfoEntity)){
					String value = DictBizCache.getValue(BizConstant.CLIENT_INVERTER_MODEL_SUPPORT_PARALLEL_DICT_CODE, factoryInfoEntity.getDeviceType());
					//判断设备是否支持并机模式(类型必须是可并机类型的逆变器)
					return ValidationUtil.isNotEmpty(value);
				}else {
					return false;
				}
			}
		}
		// 如果站点下没有逆变器，则需要返回true，则可以添加逆变器
		return true;
	}

	private void unbindWifiDongle(Long plantId, String deviceSerialNumber) {
		BladeUser user = AuthUtil.getUser();

		//发送mqtt通知设备,逆变器与站点已经解绑
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("deviceSn", deviceSerialNumber);
		jsonObject.put("topic", Constants.UNBIND_INVERTER);
		deviceIssueBiz.dataIssueToDevice(jsonObject);

		// 将电站下的 出厂设备信息 中 电池更新为未使用
		List<BatteryMapDeviceEntity> batteryMapDeviceEntities = batteryMapDeviceService.queryListByPlantIdAndSn(plantId, deviceSerialNumber);
		if (CollectionUtil.isNotEmpty(batteryMapDeviceEntities)) {
			List<String> batterySerialList = batteryMapDeviceEntities.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
			batteryExitFactoryInfoService.batchUpdate(batterySerialList, BizConstant.NUMBER_ZERO);
		}
		// 将电站下的 出厂设备信息 中 逆变器更新为未使用
		deviceExitFactoryInfoService.batchUpdate(List.of(deviceSerialNumber));
		// 删除 站点 和 逆变器 的关系
		wifiStickPlantService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, user.getAccount());
		// 删除 站点 和 电池的 关系
		batteryMapDeviceService.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, user.getAccount());
		// 删除设备21表数据
		device21Service.deleteByPlantIdAndSn(plantId,deviceSerialNumber);
		device23Service.deleteByPlantIdAndSn(plantId,deviceSerialNumber);
		device24Service.deleteByPlantIdAndSn(plantId,deviceSerialNumber);
		// 删除battery_current_status、device_current_status相关数据
		batteryCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId,deviceSerialNumber, user.getAccount());
		deviceCurrentStatusService.batchDeleteLogicByPlantIdAndSn(plantId,deviceSerialNumber, user.getAccount());
		// 删除设备告警信息
		alarmLogService.update(Wrappers.<AlarmLogEntity>lambdaUpdate()
			.set(AlarmLogEntity::getIsDeleted, BizConstant.CHAR_ONE)
			.eq(AlarmLogEntity::getPlantId, plantId)
			.eq(AlarmLogEntity::getSerialNumber, deviceSerialNumber)
			.eq(AlarmLogEntity::getIsDeleted, BizConstant.CHAR_ZERO));
		// 给站点记录重要事件
		this.saveImportantEventByUnbindInverter(plantId, deviceSerialNumber, user);
	}

	private void saveImportantEventByUnbindInverter(Long plantId, String deviceSerialNumber, BladeUser user) {
		List<ImportantEventEntity> importantEventEntityArrayList = new ArrayList<>();
		ImportantEventEntity plantImportEvent = new ImportantEventEntity();
		plantImportEvent.setEventType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_PLANT);
		plantImportEvent.setPlantId(plantId);
		plantImportEvent.setSerialNumber(deviceSerialNumber);
		plantImportEvent.setEventDate(new Date());
		// TODO 国际化
		plantImportEvent.setEventContent("Inverter is unbound");
		plantImportEvent.setEventRemark("The Inverter SN : " + deviceSerialNumber);
		plantImportEvent.setCreateUserAccount(user.getAccount());
		plantImportEvent.setCreateUser(user.getUserId());
		importantEventEntityArrayList.add(plantImportEvent);

		importantEventService.saveBatch(importantEventEntityArrayList);
	}
}
