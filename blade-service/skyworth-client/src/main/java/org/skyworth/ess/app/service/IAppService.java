package org.skyworth.ess.app.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiParam;
import org.skyworth.ess.app.vo.*;
import org.skyworth.ess.ota.entity.OtaUpdatePackEntity;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.vo.PlantDetailVO;
import org.skyworth.ess.plant.vo.PlantListVO;
import org.skyworth.ess.plant.vo.PlantVO;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

public interface IAppService {


	String addPlant(PlantEntity plant);

	Long addWifiDongle(WifiStickPlantEntity wifiStickPlantEntity);

	AppVO inverterByWifiStickSn(AppVO appVO);

	boolean inverterMatch(AppVO appVO);

	R<String> addBatteryDevice(AppVO appVO);

	IPage<PlantVO> queryPlantBySelf(AppVO appVO, Query uery);

//	AppReportHeaderVO queryPlantRunningState(AppVO appVO);

	AppReportHeaderVO queryPlantRunningStateHeaderV2(AppVO appVO);

	AppDeviceDetail queryDeviceDetail(AppVO appVO);

	AppDeviceDetail queryDeviceDetailV2(AppVO appVO);

	List<AppBatteryExitFactoryInfoVO> queryBatteryExitFactoryList(AppVO appVO);

	boolean deleteBattery(Long batteryMapDeviceId);

	R<Boolean> deletePlant(String ids);

	/**
	 * app端升级接口
	 *
	 * @param map 入参
	 * @return OtaUpdatePackEntity
	 * <AUTHOR>
	 * @since 2023/10/7 16:45
	 **/
	OtaUpdatePackEntity upgrading(Map<String, Object> map);

	List<InverterModeVO> inverterList(AppVO appVO);

	R<String> editPlant(PlantEntity plantEntity);

	R<Boolean> deleteUser(AppVO appVO);

	AppReportHeaderVO queryRunningStateChartV2(AppVO appVO);

	PlantDetailVO getPlantDetail(AppVO appVO);

	IPage<PlantListVO> queryPlantBySelfV2(AppVO appVO, Query query);

	JSONObject queryDeviceDateAndTimeZone(Long plantId);

	R<String> unbindWifiDongle(WifiStickPlantEntity wifiStickPlantEntity);

	Boolean inverterParallelEnable(AppVO appVO);
}
