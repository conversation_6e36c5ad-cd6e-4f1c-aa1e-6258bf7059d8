package org.skyworth.ess.app.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.service.IAlarmLogService;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageCondition;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageVO;
import org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity;
import org.skyworth.ess.alarmoperationrecord.service.IAlarmLogOperationRecordService;
import org.skyworth.ess.constant.DateTimeTypeEnum;
import org.springblade.common.constant.BizConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName AppAlarmLogController
 * @Description app告警信息接口
 * @Date 2024/8/17 下午2:00
 */
@Slf4j
@RestController
@RequestMapping("/app")
@Api(value = "app告警信息接口", tags = "app告警信息接口")
@AllArgsConstructor
public class AppAlarmLogController extends BladeController {

    private final IAlarmLogService alarmLogService;

    private final IAlarmLogOperationRecordService alarmLogOperationRecordService;

    /**
     * 告警管理 分页查询
     */
    @GetMapping("/alarmLog/list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "告警分页查询", notes = "告警分页查询")
    public R<IPage<AlarmLogPageVO>> list(AlarmLogPageCondition alarmLogPageCondition, Query query) {
        // 判断dateRangeType，传空就查全部时间范围的数据
        if (DateTimeTypeEnum.YEAR.getValue().equals(alarmLogPageCondition.getDateRangeType())) {
            alarmLogPageCondition.setStartDateTime(org.springblade.common.utils.DateUtil.getBeforeYearTime());
            alarmLogPageCondition.setEndDateTime(org.springblade.common.utils.DateUtil.getNowEndTime());
        } else if (DateTimeTypeEnum.MONTH.getValue().equals(alarmLogPageCondition.getDateRangeType())) {
            alarmLogPageCondition.setStartDateTime(org.springblade.common.utils.DateUtil.getBeforeMonthTime());
            alarmLogPageCondition.setEndDateTime(org.springblade.common.utils.DateUtil.getNowEndTime());
        } else if (DateTimeTypeEnum.WEEK.getValue().equals(alarmLogPageCondition.getDateRangeType())) {
            alarmLogPageCondition.setStartDateTime(org.springblade.common.utils.DateUtil.getBeforeWeekTime());
            alarmLogPageCondition.setEndDateTime(org.springblade.common.utils.DateUtil.getNowEndTime());
        } else if (DateTimeTypeEnum.DAY.getValue().equals(alarmLogPageCondition.getDateRangeType())) {
            alarmLogPageCondition.setStartDateTime(org.springblade.common.utils.DateUtil.getBeforeDayTime());
            alarmLogPageCondition.setEndDateTime(org.springblade.common.utils.DateUtil.getNowEndTime());
        }
        IPage<AlarmLogPageVO> pages = alarmLogService.list(alarmLogPageCondition, query);
        return R.data(pages);
    }

    /**
     * 告警管理 告警详情
     */
    @GetMapping("/alarmLog/detail")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "告警详情", notes = "传入id")
    public R<AlarmLogPageVO> detail(@RequestParam("id") String id) {
        return R.data(alarmLogService.detail(id));
    }

    /**
     * 告警处理记录列表
     */
    @GetMapping("/alarmLogOperationRecord/list")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "告警处理记录列表", notes = "告警处理记录列表")
    public R<List<AlarmLogOperationRecordEntity>> operationRecordList(@RequestParam("alarmLogId") String alarmLogId) {
		return R.data(alarmLogService.operationRecordList(alarmLogId));
    }

    /**
     * 告警日志操作记录 处理告警
     */
    @PostMapping("/alarmLog/operation")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "处理告警", notes = "处理告警")
    public R<Boolean> alarmLogOperation(@Valid @RequestBody AlarmLogOperationRecordEntity alarmLogOperationRecord) {
        BladeUser user = getUser();
        String userName = user.getUserName();
        alarmLogOperationRecord.setCreateUserAccount(userName);
        alarmLogOperationRecord.setUpdateUserAccount(userName);
        // 保存操作日志
        alarmLogOperationRecordService.save(alarmLogOperationRecord);
        // 如果为已处理，则需要更新主表状态
        if (BizConstant.NUMBER_ONE.equals(alarmLogOperationRecord.getStatus())) {
            alarmLogService.update(
                    Wrappers.<AlarmLogEntity>lambdaUpdate().set(AlarmLogEntity::getStatus, BizConstant.NUMBER_ONE).set(
                            AlarmLogEntity::getUpdateTime, DateUtil.now()
                    ).set(AlarmLogEntity::getUpdateUser, user.getUserId()).set(AlarmLogEntity::getUpdateUserAccount, userName).eq(AlarmLogEntity::getId, alarmLogOperationRecord.getAlarmLogId()));
        }
        return R.status(true);
    }


}
