/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ota.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.exception.vo.ExceptionLogVO;
import org.skyworth.ess.ota.entity.SoftwareUpgradeRecordEntity;
import org.skyworth.ess.ota.excel.SoftwareUpgradeRecordExcel;
import org.skyworth.ess.ota.mapper.SoftwareUpgradeRecordMapper;
import org.skyworth.ess.ota.service.ISoftwareUpgradeRecordService;
import org.skyworth.ess.ota.vo.OtaUpdatePackVO;
import org.skyworth.ess.ota.vo.SoftwareUpgradeRecordVO;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.skyworth.ess.timeshift.ITimeShiftService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备软件版本升级记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Service
public class SoftwareUpgradeRecordServiceImpl extends BaseServiceImpl<SoftwareUpgradeRecordMapper, SoftwareUpgradeRecordEntity> implements ISoftwareUpgradeRecordService {
	@Autowired
	private ITimeShiftService timeShiftService;
	@Autowired
	private IWifiStickPlantService wifiStickPlantService;


	@Override
	public IPage<SoftwareUpgradeRecordVO> selectSoftwareUpgradeRecordPage(IPage<SoftwareUpgradeRecordVO> page, SoftwareUpgradeRecordVO softwareUpgradeRecord) {
		return page.setRecords(baseMapper.selectSoftwareUpgradeRecordPage(page, softwareUpgradeRecord));
	}


	@Override
	public List<SoftwareUpgradeRecordExcel> exportSoftwareUpgradeRecord(Wrapper<SoftwareUpgradeRecordEntity> queryWrapper) {
		List<SoftwareUpgradeRecordExcel> softwareUpgradeRecordList = baseMapper.exportSoftwareUpgradeRecord(queryWrapper);
		//softwareUpgradeRecordList.forEach(softwareUpgradeRecord -> {
		//	softwareUpgradeRecord.setTypeName(DictCache.getValue(DictEnum.YES_NO, SoftwareUpgradeRecord.getType()));
		//});
		return softwareUpgradeRecordList;
	}

	@Override
	public void batchInsert(List<OtaUpdatePackVO> otaUpdatePackVOList) {
		baseMapper.batchInsert(otaUpdatePackVOList);
	}

	@Override
	public JSONObject getByIdFeign(Long id) {
		SoftwareUpgradeRecordEntity byId = this.getById(id);
        return (JSONObject) JSON.toJSON(byId);
	}

	@Override
	public IPage<SoftwareUpgradeRecordEntity> selectSoftwareUpgradeRecord(Map<String, Object> softwareUpgradeRecord, Query query) {
		IPage<SoftwareUpgradeRecordEntity> pages = this.page(Condition.getPage(query), Condition.getQueryWrapper(softwareUpgradeRecord, SoftwareUpgradeRecordEntity.class).orderByDesc("update_time", "id"));
		List<SoftwareUpgradeRecordEntity> resultRecords = pages.getRecords();

		if (!CollectionUtils.isNullOrEmpty(resultRecords)) {
			List<String> snList = resultRecords.stream().map(SoftwareUpgradeRecordEntity::getSerialNumber).distinct().collect(Collectors.toList());
			List<WifiStickPlantEntity> wifiStickPlantEntities = wifiStickPlantService.queryByDeviceSerialNumberList(snList);
			Map<String,Long> collect = wifiStickPlantEntities.stream().collect(Collectors.toMap(WifiStickPlantEntity::getDeviceSerialNumber,WifiStickPlantEntity::getPlantId));
			List<Long> plantIdList = wifiStickPlantEntities.stream().map(WifiStickPlantEntity::getPlantId).distinct().collect(Collectors.toList());
			resultRecords.stream().forEach(record -> {
				if (collect.containsKey(record.getSerialNumber())){
					record.setPlantId(collect.get(record.getSerialNumber()));
				}
			});
			timeShiftService.getAndReturnList(resultRecords,"updateTime",plantIdList);
			pages.setRecords(resultRecords);
		}
		return pages;
	}

}
