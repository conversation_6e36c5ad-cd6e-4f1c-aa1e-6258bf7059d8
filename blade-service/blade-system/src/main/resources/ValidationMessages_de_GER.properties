system.agent.agentNumber.can.not.empty=Rollout Manager ID darf nicht leer sein
system.agent.agentNumber.is.not.exits=Verteiler ID existiert nicht
system.error=Systemfehler
system.user.account.is.exits=Das Benutzerkonto ist besetzt
system.user.phone.is.exits=Die Rufnummer ist besetzt
system.user.phone.is.exits.to.another.tenant=<PERSON><PERSON> ist an ein anderes Konto gebunden. Bitte wenden Sie sich an den Systemmanager.
system.user.the.verification.code.is.error=Der Verifizierungscode ist Fehler
system.user.the.is.not.exist=Der Benutzer existiert nicht.
system.user.email.is.exits=Die E-Mail-Adresse wurde registriert
system.exception.delete.child.nodes=Entfernen sie zuerst das knotenpunkt!
system.exception.delete.child.nodes.interface=Entfernen Sie zuerst die untergeordneten Schnittstellen!
system.dict.key.value.exist=Der aktuelle kr\u00FCmmwert f\u00FCr den w\u00F6rterbuch existiert bereits!
system.exception.not.resubmit=Bitte nicht erneut einreichen
system.attach.info.empty=Anhangsinformationen sind leer.
system.exception.code.execution=Eine anomalie im business-code
system.exception.cannot.select.parent.itself=Knotenpunkte w\u00E4hlen sich nicht selbst!
system.exception.cannot.config.superadmin.role=Keine Berechtigung zum Konfigurieren der Super-Management-Rolle!
system.exception.cannot.config.admin.role=Keine Berechtigung zum Konfigurieren der Administratorrolle!
system.exception.cannot.create.superadmin.role=Keine Berechtigung zum Erstellen einer Super-Management-Rolle
system.exception.cannot.delete.super.tenant=Sie kann nicht gel\u00F6scht werden.
system.exception.menu.name.number.exist=Daf\u00FCr gibt es bereits einen men\u00FCnamen und eine nummer!
system.exception.menu.parent.only.menu.type=Knotenpunkte w\u00E4hlen nur den men\u00FCtyp aus!
system.user.password.cannot.empty=Das Passwort kann nicht leer sein
system.user.tenant.quota.max=Der aktuelle Mandant hat das maximale Kontokontingent erreicht
system.user.enter.confirm.password=Bitte geben sie das korrekte best\u00E4tigungspasswort ein!
system.user.original.password.incorrect=Der urspr\u00FCngliche code ist falsch!
system.user.tenant.info.error=Fehler bei der Mieterinformation!
system.user.third.login.error=Third-party login information error!
system.user.account.connot.delete=Dieser zugang ist nicht l\u00F6schen!
system.user.account.abnormal=Das Konto des aktuellen Benutzers [{%s}] ist abnormal!
system.user.account.not.exist=Der aktuelle Benutzer [{%s}] existiert nicht!
system.user.account.is.exist=Der aktuelle Benutzer [{%s}] existiert bereits!
