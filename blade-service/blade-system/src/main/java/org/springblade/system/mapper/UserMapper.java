/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.system.entity.User;
import org.springblade.system.excel.UserExcel;
import org.springblade.system.vo.UserInfoMappingVO;
import org.springblade.system.vo.UserInfoVO;
import org.springblade.system.vo.UserRegistrationVO;
import org.springblade.system.vo.UserStatisticsVO;

import java.util.List;
import java.util.Map;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface UserMapper extends BaseMapper<User> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param user
	 * @param deptIdList
	 * @param tenantId
	 * @return
	 */
	List<User> selectUserPage(IPage<User> page, @Param("user") User user, @Param("deptIdList") List<Long> deptIdList, @Param("tenantId") String tenantId);

	/**
	 * 获取用户
	 *
	 * @param tenantId
	 * @param account
	 * @return
	 */
	User getUser(String tenantId, String account);

	/**
	 * 获取导出用户数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<UserExcel> exportUser(@Param("ew") Wrapper<User> queryWrapper);

	int updateUser(@Param("userInfo") UserInfoVO userInfo);

	List<User> userInfoByCondition(@Param("params") User user);

	void appendRole(@Param("roleIds") String roleIds, @Param("userId") Long userId, @Param("deptId") Long deptId);

	List<User> roleUser(IPage<User> page, @Param("roleId") String roleId, @Param("tenantId") String tenantId);

	/**
	 * 根据用户ID查询所有用户列表（包含删除的，工作流用）
	 *
	 * @param userId 用户ID
	 * @return 用户列表
	 */
	List<User> listAllByUser(List<Long> userId);

	/**
	 * 便携式查询app注册信息
	 *
	 * @param map 入参
	 * @return List<UserStatisticsVO>
	 * <AUTHOR>
	 * @since 2024/1/27 10:12
	 **/
	List<UserStatisticsVO> singleDayRegisterInfo(@Param("ew") Map<String, Object> map);

	/**
	 * 便携式查询app注册信息
	 *
	 * @param map 入参
	 * @return List<UserStatisticsVO>
	 * <AUTHOR>
	 * @since 2024/1/27 10:12
	 **/
	List<UserRegistrationVO> singleDayRegisterExcelInfo(@Param("ew") Map<String, Object> map);


	List<User> selectMappingUser(@Param("userIds") List<Long> userIds);

	List<User> selectMappingSourceUser(@Param("userIds") List<Long> userIds);

	/**
	 * 不使用框架带的租户id，跨租户查询用户是否存在
	 *
	 * @param user
	 * @return
	 */
	@TenantIgnore
	User getUserIgnoreTenant(@Param("user") User user);

	int updateUserIgnoreTenant(@Param("user") User user);

	@TenantIgnore
	List<User> batchGetUserIgnoreTenant(@Param("userIdList") List<Long> userIdList);

	@TenantIgnore
	List<UserInfoMappingVO> batchGetClientUserByEpcUserIgnoreTenant(@Param("userIdList") List<Long> userIdList);

	@TenantIgnore
	int batchUpdateUserIgnoreTenant(@Param("listUser") List<User> listUser);

	/**
	 * 代理商新增用户的时候，更新设备端用户信息和维护部门以及角色信息
	 *
	 * @param userList     入参
	 * @param updateUserId 入参
	 * @return int
	 * <AUTHOR>
	 * @since 2024/3/11 17:39
	 **/
	int updateUserListInfo(@Param("list") List<User> userList, @Param("updateUserId") Long updateUserId);

	/**
	 * 代理商删除用户的时候，更新设备端用户信息和维护部门以及角色信息
	 *
	 * @param userList     入参
	 * @param updateUserId 入参
	 * @return int
	 * <AUTHOR>
	 * @since 2024/3/11 17:39
	 **/
	int deleteUserListInfo(@Param("list") List<User> userList, @Param("updateUserId") Long updateUserId);

	/**
	 * 删除用户和代理商部门的关系
	 *
	 * @param userList     入参
	 * @param updateUserId 修改人
	 * @return int
	 * <AUTHOR>
	 * @since 2024/3/12 9:55
	 **/
	int deleteAgentRollOutManager(@Param("list") List<User> userList, @Param("updateUserId") Long updateUserId);

	int deleteUserListInfoForClient(@Param("list") List<User> userList, @Param("updateUserId") Long updateUserId);


	int insertUserListInfoForClient(@Param("list") List<User> userList);

	User getUserWithPhoneDiallingCode(@Param("tenantId")String tenantId,@Param("account") String account,@Param("phoneDiallingCode") String phoneDiallingCode);
}
